#!/bin/bash

# Análisis específico de HttpClient en el proyecto
# Uso: ./httpclient_analysis.sh

echo "=== ANÁLISIS DETALLADO DE HTTPCLIENT ==="
echo "========================================"

LIB_DIR="portalconciliacion/Codigo Servicio/wsConciliacion/WebContent/WEB-INF/lib"
SRC_DIR="portalconciliacion/Codigo Servicio/wsConciliacion/src"
CLASSPATH_FILE="portalconciliacion/Codigo Servicio/wsConciliacion/.classpath"

echo ""
echo "1. ARCHIVOS HTTPCLIENT ENCONTRADOS:"
echo "=================================="

# Buscar todos los archivos relacionados con httpclient
echo "En directorio lib:"
find "$LIB_DIR" -name "*http*" -type f 2>/dev/null | while read file; do
    size=$(ls -lh "$file" | awk '{print $5}')
    echo "  ✅ $(basename "$file") ($size)"
done

echo ""
echo "2. CONFIGURACIÓN EN .CLASSPATH:"
echo "=============================="
if [ -f "$CLASSPATH_FILE" ]; then
    echo "Entradas de HttpClient en .classpath:"
    grep -i "httpclient\|httpcore" "$CLASSPATH_FILE" | while read line; do
        jar_name=$(echo "$line" | sed 's/.*path="[^"]*\/\([^"]*\)".*/\1/')
        echo "  📋 $jar_name"
    done
else
    echo "❌ Archivo .classpath no encontrado"
fi

echo ""
echo "3. ANÁLISIS DE VERSIONES:"
echo "========================"

# Verificar HttpClient 4.x
if [ -f "$LIB_DIR/httpclient-4.5.14.jar" ]; then
    echo "✅ HttpClient 4.x encontrado:"
    echo "   📦 httpclient-4.5.14.jar"
    echo "   📋 Configurado en .classpath: SÍ"
    echo "   🎯 Versión: Apache HttpComponents Client 4.5.14"
fi

# Verificar HttpClient 5.x
httpclient5_files=$(find "$LIB_DIR" -name "*httpclient5*" -o -name "*httpcore5*" 2>/dev/null)
if [ -n "$httpclient5_files" ]; then
    echo ""
    echo "✅ HttpClient 5.x encontrado:"
    echo "$httpclient5_files" | while read file; do
        echo "   📦 $(basename "$file")"
    done
    
    # Verificar si está en classpath
    if grep -q "httpclient5\|httpcore5" "$CLASSPATH_FILE" 2>/dev/null; then
        echo "   📋 Configurado en .classpath: SÍ"
    else
        echo "   📋 Configurado en .classpath: ❌ NO"
    fi
fi

echo ""
echo "4. USO EN CÓDIGO JAVA:"
echo "===================="

# Buscar imports de HttpClient 4.x
echo "Imports de HttpClient 4.x (org.apache.http):"
http4_usage=$(find "$SRC_DIR" -name "*.java" -exec grep -l "import.*org\.apache\.http" {} \; 2>/dev/null)
if [ -n "$http4_usage" ]; then
    echo "$http4_usage" | while read file; do
        echo "  ✅ $(basename "$file")"
        grep "import.*org\.apache\.http" "$file" | head -3 | sed 's/^/     ├─ /'
    done
else
    echo "  ❌ No se encontraron imports de HttpClient 4.x"
fi

echo ""
echo "Imports de HttpClient 5.x (org.apache.hc):"
http5_usage=$(find "$SRC_DIR" -name "*.java" -exec grep -l "import.*org\.apache\.hc" {} \; 2>/dev/null)
if [ -n "$http5_usage" ]; then
    echo "$http5_usage" | while read file; do
        echo "  ✅ $(basename "$file")"
        grep "import.*org\.apache\.hc" "$file" | head -3 | sed 's/^/     ├─ /'
    done
else
    echo "  ❌ No se encontraron imports de HttpClient 5.x"
fi

echo ""
echo "5. ANÁLISIS DE DEPENDENCIAS:"
echo "============================"

# Verificar quién podría estar usando HttpClient como dependencia transitiva
echo "Posibles usuarios de HttpClient como dependencia transitiva:"

# Axis2 podría usar HttpClient
if [ -f "$LIB_DIR/axis2-transport-http-2.0.0.jar" ]; then
    echo "  🔍 axis2-transport-http-2.0.0.jar"
    echo "     └─ Podría usar HttpClient para transporte HTTP"
fi

# Verificar en POM de Axis2 si está disponible
if [ -f "$LIB_DIR/axis2-kernel-2.0.0.jar" ]; then
    echo "  🔍 axis2-kernel-2.0.0.jar"
    echo "     └─ Verificando dependencias..."
    
    temp_dir=$(mktemp -d)
    if jar -xf "$LIB_DIR/axis2-kernel-2.0.0.jar" -C "$temp_dir" META-INF/maven/org.apache.axis2/axis2-kernel/pom.xml 2>/dev/null; then
        if grep -q "httpclient\|httpcore" "$temp_dir/META-INF/maven/org.apache.axis2/axis2-kernel/pom.xml" 2>/dev/null; then
            echo "     ✅ HttpClient encontrado como dependencia en POM"
        else
            echo "     ❌ HttpClient NO encontrado en POM"
        fi
    fi
    rm -rf "$temp_dir"
fi

echo ""
echo "6. RESUMEN Y RECOMENDACIONES:"
echo "============================"

echo ""
echo "📊 ESTADO ACTUAL:"
if [ -f "$LIB_DIR/httpclient-4.5.14.jar" ]; then
    echo "  ✅ HttpClient 4.5.14 está presente y configurado"
fi

if [ -n "$httpclient5_files" ]; then
    echo "  ⚠️  HttpClient 5.x está presente pero NO configurado en classpath"
fi

echo ""
echo "🎯 RESPUESTA A TU PREGUNTA:"
echo "httpclient-5.4.2.jar específicamente: ❌ NO ENCONTRADO"
echo ""
echo "Sin embargo, se encontraron:"
echo "  • httpclient-4.5.14.jar (configurado y posiblemente usado)"
echo "  • httpclient5-5.5.jar (presente pero no configurado)"
echo ""
echo "💡 POSIBLES RAZONES:"
echo "  1. El proyecto usa HttpClient 4.x (no 5.x)"
echo "  2. HttpClient 5.x podría ser una dependencia no utilizada"
echo "  3. Podría haber sido agregado para migración futura"
echo ""
echo "🔧 PARA VERIFICAR USO REAL:"
echo "  1. Revisa logs de aplicación en runtime"
echo "  2. Usa herramientas de profiling"
echo "  3. Analiza stack traces de la aplicación"
