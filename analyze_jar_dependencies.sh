#!/bin/bash

# Script para analizar dependencias transitivas de JARs
# Uso: ./analyze_jar_dependencies.sh

LIB_DIR="portalconciliacion/Codigo Servicio/wsConciliacion/WebContent/WEB-INF/lib"

echo "=== ANÁLISIS DE DEPENDENCIAS DE JARS ==="
echo "Directorio: $LIB_DIR"
echo "========================================"

# Librerías que queremos verificar si son transitivas
TARGET_LIBS=(
    "logback-classic-1.2.3.jar"
    "logback-core-1.2.3.jar"
    "guava-30.1.1-android.jar"
    "woodstox-core-6.2.8.jar"
    "commons-io-2.11.0.jar"
    "httpclient-5.4.2.jar"
    "xalan-2.7.2.jar"
    "junit-4.7.jar"
    "junit-4.11.jar"
    "junit-4.12.jar"
    "apache-mime4j-core-0.7.2.jar"
    "apache-mime4j-core-0.8.6.jar"
    "commons-codec-1.11.jar"
)

# Función para extraer dependencias de un JAR
extract_dependencies() {
    local jar_file=$1
    local jar_name=$(basename "$jar_file")
    
    echo ""
    echo "--- Analizando $jar_name ---"
    
    if [ ! -f "$jar_file" ]; then
        echo "❌ JAR no encontrado: $jar_file"
        return
    fi
    
    # Crear directorio temporal
    local temp_dir=$(mktemp -d)
    
    # Extraer MANIFEST.MF
    if jar -xf "$jar_file" -C "$temp_dir" META-INF/MANIFEST.MF 2>/dev/null; then
        echo "📄 MANIFEST.MF encontrado:"
        if [ -f "$temp_dir/META-INF/MANIFEST.MF" ]; then
            grep -E "(Class-Path|Bundle-ClassPath|Require-Bundle)" "$temp_dir/META-INF/MANIFEST.MF" || echo "  No se encontraron dependencias en MANIFEST"
        fi
    fi
    
    # Buscar archivos POM
    local pom_files=$(jar -tf "$jar_file" | grep -E "pom\.(xml|properties)")
    if [ -n "$pom_files" ]; then
        echo "📄 Archivos POM encontrados:"
        echo "$pom_files" | while read pom_file; do
            echo "  ├─ $pom_file"
            if [[ "$pom_file" == *.xml ]]; then
                jar -xf "$jar_file" -C "$temp_dir" "$pom_file" 2>/dev/null
                if [ -f "$temp_dir/$pom_file" ]; then
                    echo "    Dependencias en POM:"
                    grep -A 3 -B 1 "<artifactId>" "$temp_dir/$pom_file" | grep -E "(groupId|artifactId|version)" | head -15 | sed 's/^/      /'
                fi
            fi
        done
    else
        echo "📄 No se encontraron archivos POM"
    fi
    
    # Limpiar directorio temporal
    rm -rf "$temp_dir"
}

# Función para verificar si una librería objetivo está presente
check_target_library() {
    local target_lib=$1
    echo ""
    echo "🔍 Verificando: $target_lib"
    
    # Buscar JAR exacto
    if [ -f "$LIB_DIR/$target_lib" ]; then
        echo "✅ ENCONTRADO: $target_lib está presente como dependencia directa"
        return
    fi
    
    # Buscar versiones similares
    local base_name=$(echo "$target_lib" | sed 's/-[0-9].*//')
    local similar_jars=$(find "$LIB_DIR" -name "${base_name}*.jar" 2>/dev/null)
    
    if [ -n "$similar_jars" ]; then
        echo "⚠️  VERSIÓN DIFERENTE encontrada:"
        echo "$similar_jars" | while read jar; do
            echo "   ├─ $(basename "$jar")"
        done
    else
        echo "❌ NO ENCONTRADO: $target_lib no está presente"
        
        # Buscar en dependencias transitivas de JARs principales
        echo "   🔍 Buscando como dependencia transitiva..."
        local found_in_transitive=false
        
        for main_jar in "$LIB_DIR"/*.jar; do
            if [ -f "$main_jar" ]; then
                local jar_content=$(jar -tf "$main_jar" 2>/dev/null | grep -i "$(echo "$base_name" | tr '-' '.')")
                if [ -n "$jar_content" ]; then
                    echo "   ✅ Posible dependencia transitiva en: $(basename "$main_jar")"
                    found_in_transitive=true
                fi
            fi
        done
        
        if [ "$found_in_transitive" = false ]; then
            echo "   ❌ No encontrado como dependencia transitiva"
        fi
    fi
}

echo ""
echo "=== VERIFICACIÓN DE LIBRERÍAS OBJETIVO ==="
for target_lib in "${TARGET_LIBS[@]}"; do
    check_target_library "$target_lib"
done

echo ""
echo "=== ANÁLISIS DE JARS PRINCIPALES ==="
echo "Analizando dependencias de los JARs principales del proyecto..."

# Analizar algunos JARs principales
MAIN_JARS=(
    "axis2-kernel-2.0.0.jar"
    "httpclient-4.5.14.jar"
    "itext7-kernel-7.1.19.jar"
    "bcprov-jdk18on-1.79.jar"
)

for jar_name in "${MAIN_JARS[@]}"; do
    if [ -f "$LIB_DIR/$jar_name" ]; then
        extract_dependencies "$LIB_DIR/$jar_name"
    else
        echo ""
        echo "--- $jar_name ---"
        echo "❌ JAR no encontrado"
    fi
done

echo ""
echo "=== RESUMEN ==="
echo "✅ = Librería encontrada"
echo "⚠️  = Versión diferente encontrada"
echo "❌ = Librería no encontrada"
echo ""
echo "Para más detalles sobre dependencias transitivas, revisa los archivos POM extraídos arriba."
