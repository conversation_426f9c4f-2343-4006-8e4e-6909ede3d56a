#!/bin/bash

# Análisis profundo de dependencias transitivas
# Uso: ./deep_dependency_analysis.sh

echo "=== ANÁLISIS PROFUNDO DE DEPENDENCIAS TRANSITIVAS ==="
echo "===================================================="

LIB_DIR="portalconciliacion/Codigo Servicio/wsConciliacion/WebContent/WEB-INF/lib"

# Función para extraer y analizar POM de un JAR
analyze_jar_pom() {
    local jar_file=$1
    local jar_name=$(basename "$jar_file")
    
    echo ""
    echo "🔍 Analizando: $jar_name"
    echo "================================"
    
    # Crear directorio temporal
    local temp_dir=$(mktemp -d)
    
    # Buscar archivos POM
    local pom_files=$(jar -tf "$jar_file" 2>/dev/null | grep "pom.xml$")
    
    if [ -n "$pom_files" ]; then
        echo "📄 POM encontrado, extrayendo dependencias..."
        
        # Extraer el primer POM encontrado
        local first_pom=$(echo "$pom_files" | head -1)
        jar -xf "$jar_file" -C "$temp_dir" "$first_pom" 2>/dev/null
        
        if [ -f "$temp_dir/$first_pom" ]; then
            echo ""
            echo "📋 Dependencias declaradas:"
            
            # Extraer dependencias del POM
            grep -A 4 "<dependency>" "$temp_dir/$first_pom" 2>/dev/null | \
            grep -E "(groupId|artifactId|version|scope)" | \
            sed 's/.*<\([^>]*\)>\([^<]*\)<.*/  \1: \2/' | \
            while read line; do
                if [[ "$line" == *"groupId"* ]]; then
                    echo ""
                    echo -n "  📦 "
                fi
                echo "$line"
            done
            
            echo ""
            echo "🎯 Dependencias que coinciden con tu lista:"
            
            # Buscar dependencias específicas
            if grep -q "commons-io" "$temp_dir/$first_pom" 2>/dev/null; then
                echo "  ✅ commons-io encontrado como dependencia"
            fi
            
            if grep -q "junit" "$temp_dir/$first_pom" 2>/dev/null; then
                echo "  ✅ junit encontrado como dependencia"
            fi
            
            if grep -q "commons-codec" "$temp_dir/$first_pom" 2>/dev/null; then
                echo "  ✅ commons-codec encontrado como dependencia"
            fi
            
            if grep -q "logback" "$temp_dir/$first_pom" 2>/dev/null; then
                echo "  ✅ logback encontrado como dependencia"
            fi
            
            if grep -q "guava" "$temp_dir/$first_pom" 2>/dev/null; then
                echo "  ✅ guava encontrado como dependencia"
            fi
            
            if grep -q "woodstox" "$temp_dir/$first_pom" 2>/dev/null; then
                echo "  ✅ woodstox encontrado como dependencia"
            fi
            
            if grep -q "xalan" "$temp_dir/$first_pom" 2>/dev/null; then
                echo "  ✅ xalan encontrado como dependencia"
            fi
            
            if grep -q "mime4j" "$temp_dir/$first_pom" 2>/dev/null; then
                echo "  ✅ mime4j encontrado como dependencia"
            fi
        fi
    else
        echo "❌ No se encontró archivo POM en este JAR"
    fi
    
    # Limpiar
    rm -rf "$temp_dir"
}

# Analizar JARs principales que podrían tener dependencias transitivas
echo "Analizando JARs principales del proyecto..."

MAIN_JARS=(
    "axis2-kernel-2.0.0.jar"
    "axis2-transport-http-2.0.0.jar"
    "axis2-jaxws-2.0.0.jar"
    "httpclient-4.5.14.jar"
    "httpcore-4.4.16.jar"
    "itext7-kernel-7.1.19.jar"
    "itext7-sign-7.1.19.jar"
    "bcprov-jdk18on-1.79.jar"
    "postgresql-42.7.5.jar"
)

for jar_name in "${MAIN_JARS[@]}"; do
    if [ -f "$LIB_DIR/$jar_name" ]; then
        analyze_jar_pom "$LIB_DIR/$jar_name"
    else
        echo ""
        echo "🔍 $jar_name"
        echo "❌ JAR no encontrado en el proyecto"
    fi
done

echo ""
echo "=== COMANDO PARA VERIFICAR DEPENDENCIAS MANUALMENTE ==="
echo "======================================================="
echo ""
echo "Para verificar dependencias transitivas manualmente, usa estos comandos:"
echo ""
echo "1. Extraer POM de un JAR específico:"
echo "   jar -tf archivo.jar | grep pom.xml"
echo "   jar -xf archivo.jar META-INF/maven/grupo/artefacto/pom.xml"
echo ""
echo "2. Buscar clases específicas en JARs:"
echo "   jar -tf archivo.jar | grep -i 'nombre_clase'"
echo ""
echo "3. Verificar MANIFEST.MF:"
echo "   jar -xf archivo.jar META-INF/MANIFEST.MF"
echo "   cat META-INF/MANIFEST.MF"
echo ""
echo "4. Buscar imports en código Java:"
echo "   grep -r 'import.*paquete' directorio_src/"
echo ""
echo "5. Listar todas las clases en un JAR:"
echo "   jar -tf archivo.jar | grep '\.class$' | head -20"

echo ""
echo "=== HERRAMIENTAS ADICIONALES RECOMENDADAS ==="
echo "============================================="
echo ""
echo "Para análisis más profundo, considera usar:"
echo ""
echo "1. Maven Dependency Plugin (si conviertes a Maven):"
echo "   mvn dependency:tree"
echo "   mvn dependency:analyze"
echo ""
echo "2. JDeps (incluido en JDK):"
echo "   jdeps -cp 'lib/*' archivo.jar"
echo ""
echo "3. Herramientas online:"
echo "   - Maven Central Repository"
echo "   - MVN Repository"
echo "   - JarAnalyzer"
