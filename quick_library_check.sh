#!/bin/bash

# Script rápido para verificar uso de librerías específicas
# Uso: ./quick_library_check.sh

echo "=== VERIFICACIÓN RÁPIDA DE LIBRERÍAS ==="
echo "========================================"

SOURCE_DIR="portalconciliacion/Codigo Servicio/wsConciliacion/src"
LIB_DIR="portalconciliacion/Codigo Servicio/wsConciliacion/WebContent/WEB-INF/lib"

# Función para verificar una librería
check_library() {
    local lib_name=$1
    local package_pattern=$2
    local jar_pattern=$3
    
    echo ""
    echo "🔍 Verificando: $lib_name"
    echo "   Paquete: $package_pattern"
    
    # 1. Verificar si está en el directorio lib
    local jar_found=$(find "$LIB_DIR" -name "*${jar_pattern}*" 2>/dev/null)
    if [ -n "$jar_found" ]; then
        echo "   📦 JAR encontrado: $(basename "$jar_found")"
    else
        echo "   📦 JAR: ❌ No encontrado"
    fi
    
    # 2. Verificar imports en código
    local imports_found=$(find "$SOURCE_DIR" -name "*.java" -exec grep -l "import.*$package_pattern" {} \; 2>/dev/null)
    if [ -n "$imports_found" ]; then
        echo "   💻 CÓDIGO: ✅ Usado en código"
        echo "$imports_found" | head -3 | while read file; do
            echo "      ├─ $(basename "$file")"
        done
    else
        echo "   💻 CÓDIGO: ❌ No usado en código"
    fi
    
    # 3. Verificar uso sin import (fully qualified)
    local fqn_usage=$(find "$SOURCE_DIR" -name "*.java" -exec grep -l "$package_pattern\." {} \; 2>/dev/null)
    if [ -n "$fqn_usage" ]; then
        echo "   🔗 USO DIRECTO: ✅ Usado con nombre completo"
    fi
    
    # 4. Resultado final
    if [ -n "$jar_found" ] && [ -n "$imports_found" ]; then
        echo "   📊 RESULTADO: ✅ LIBRERÍA USADA"
    elif [ -n "$jar_found" ] && [ -z "$imports_found" ]; then
        echo "   📊 RESULTADO: ⚠️  LIBRERÍA PRESENTE PERO NO USADA"
    elif [ -z "$jar_found" ] && [ -n "$imports_found" ]; then
        echo "   📊 RESULTADO: ⚠️  CÓDIGO USA LIBRERÍA PERO JAR NO ENCONTRADO"
    else
        echo "   📊 RESULTADO: ❌ LIBRERÍA NO PRESENTE NI USADA"
    fi
}

# Verificar librerías específicas
echo "Verificando librerías de tu lista..."

check_library "Logback Classic" "ch.qos.logback.classic" "logback-classic"
check_library "Logback Core" "ch.qos.logback.core" "logback-core"
check_library "BouncyCastle Provider" "org.bouncycastle" "bcprov"
check_library "BouncyCastle PKIX" "org.bouncycastle" "bcpkix"
check_library "Google Guava" "com.google.common" "guava"
check_library "Woodstox Core" "com.ctc.wstx" "woodstox"
check_library "Commons IO" "org.apache.commons.io" "commons-io"
check_library "Apache HttpClient" "org.apache.http" "httpclient"
check_library "Apache Xalan" "org.apache.xalan" "xalan"
check_library "Commons Net" "org.apache.commons.net" "commons-net"
check_library "JUnit" "org.junit" "junit"
check_library "Apache Mime4j" "org.apache.james.mime4j" "mime4j"
check_library "Commons Codec" "org.apache.commons.codec" "commons-codec"

echo ""
echo "=== RESUMEN DE JARS PRESENTES ==="
echo "JARs encontrados en $LIB_DIR:"
ls -1 "$LIB_DIR"/*.jar 2>/dev/null | while read jar; do
    echo "  ├─ $(basename "$jar")"
done

echo ""
echo "=== LEYENDA ==="
echo "✅ = Encontrado/Usado"
echo "❌ = No encontrado/No usado"
echo "⚠️  = Presente pero no usado / Usado pero no presente"
