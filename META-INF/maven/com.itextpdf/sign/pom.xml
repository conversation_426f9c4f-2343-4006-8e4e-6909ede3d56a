<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.itextpdf</groupId>
    <artifactId>root</artifactId>
    <version>7.1.19</version>
  </parent>
  <artifactId>sign</artifactId>
  <name>iText 7 - sign</name>
  <url>https://itextpdf.com/</url>
  <dependencies>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>forms</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>kernel</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>layout</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>pdfa</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk18on</artifactId>
      <version>${bouncycastle.version}</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk18on</artifactId>
      <version>${bouncycastle.version}</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.itextpdf</groupId>
      <artifactId>pdftest</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <configuration>
          <includes>
            <include>**/*.java</include>
          </includes>
          <groups>${integrationtests}</groups>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <groups>${unittests}</groups>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>