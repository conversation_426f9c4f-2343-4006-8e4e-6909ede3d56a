<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one
  ~ or more contributor license agreements. See the NOTICE file
  ~ distributed with this work for additional information
  ~ regarding copyright ownership. The ASF licenses this file
  ~ to you under the Apache License, Version 2.0 (the
  ~ "License"); you may not use this file except in compliance
  ~ with the License. You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing,
  ~ software distributed under the License is distributed on an
  ~ "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  ~ KIND, either express or implied. See the License for the
  ~ specific language governing permissions and limitations
  ~ under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.apache.axis2</groupId>
        <artifactId>axis2</artifactId>
        <version>2.0.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>axis2-jaxws</artifactId>

    <name>Apache Axis2 - JAXWS</name>
    <description>Axis2 JAXWS Implementation</description>
    <url>http://axis.apache.org/axis2/java/core/</url>

    <scm>
        <connection>scm:git:https://gitbox.apache.org/repos/asf/axis-axis2-java-core.git</connection>
        <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/axis-axis2-java-core.git</developerConnection>
        <url>https://gitbox.apache.org/repos/asf?p=axis-axis2-java-core.git;a=summary</url>
        <tag>v2.0.0</tag>
    </scm>

    <dependencies>
        <dependency>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-annotation_1.0_spec</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-jaxws_2.2_spec</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.axis2</groupId>
            <artifactId>axis2-kernel</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.axis2</groupId>
            <artifactId>axis2-saaj</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.axis2</groupId>
            <artifactId>axis2-metadata</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.mail</groupId>
            <artifactId>jakarta.mail-api</artifactId>
        </dependency>
        <dependency>
            <groupId>xml-resolver</groupId>
            <artifactId>xml-resolver</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-xjc</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>
	    <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-rt</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.ws</groupId>
            <artifactId>jakarta.xml.ws-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.axis2</groupId>
            <artifactId>axis2-transport-http</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.axis2</groupId>
            <artifactId>axis2-transport-local</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>        
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.xmlunit</groupId>
            <artifactId>xmlunit-legacy</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.ws.commons.axiom</groupId>
            <artifactId>xml-truth</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>addressing</artifactId>
            <version>${project.version}</version>
            <type>mar</type>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <sourceDirectory>src</sourceDirectory>
        <testSourceDirectory>test</testSourceDirectory>
        <resources>
            <resource>
                <directory>conf</directory>
                <includes>
                    <include>**/*.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
		<!-- AXIS2-6051, this directory is crucial
                     as it defines the file
                     resources/META-INF/services/jakarta.xml.ws.spi.Provider    
		     which contains org.apache.axis2.jaxws.spi.Provider.	    
		     If this file is missing or	gets renamed, 
                     various errors can occur such as 
                     "WSDL Metadata not available"

                -->	    
                <directory>resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>test</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.wsdl</include>
                    <include>**/*.properties</include>
                </includes>
            </testResource>
            <testResource>
                <directory>test-resources</directory>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <artifactId>maven-remote-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <resourceBundles>
                                <resourceBundle>org.apache.axis2:axis2-resource-bundle:${project.version}</resourceBundle>
                            </resourceBundles>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.veithen.maven</groupId>
                <artifactId>xjc-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>xjc-echo</id>
                        <goals>
                            <goal>generate-test-sources</goal>
                        </goals>
                        <configuration>
                            <files>
                                <file>test-resources/xsd/echo.xsd</file>
                            </files>
                            <outputDirectory>${project.build.directory}/generated-test-sources/jaxb/echo</outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>xjc-stock1</id>
                        <goals>
                            <goal>generate-test-sources</goal>
                        </goals>
                        <configuration>
                            <files>
                                <file>test-resources/xsd/stock1.xsd</file>
                            </files>
                            <outputDirectory>${project.build.directory}/generated-test-sources/jaxb/stock1</outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>xjc-stock2</id>
                        <goals>
                            <goal>generate-test-sources</goal>
                        </goals>
                        <configuration>
                            <files>
                                <file>test-resources/xsd/stock2.xsd</file>
                            </files>
                            <outputDirectory>${project.build.directory}/generated-test-sources/jaxb/stock2</outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>xjc-samplemtom</id>
                        <goals>
                            <goal>generate-test-sources</goal>
                        </goals>
                        <configuration>
                            <files>
                                <file>test-resources/xsd/samplemtom.xsd</file>
                            </files>
                            <outputDirectory>${project.build.directory}/generated-test-sources/jaxb/samplemtom</outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>xjc-ProxyDocLitWrapped</id>
                        <goals>
                            <goal>generate-test-sources</goal>
                        </goals>
                        <configuration>
                            <schemaLanguage>WSDL</schemaLanguage>
                            <files>
                                <file>test-resources/wsdl/ProxyDocLitWrapped.wsdl</file>
                            </files>
                            <packageName>org.test.proxy.doclitwrapped</packageName>
                            <outputDirectory>${project.build.directory}/generated-test-sources/jaxb/ProxyDocLitWrapped</outputDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>xjc-AddNumbers</id>
                        <goals>
                            <goal>generate-test-sources</goal>
                        </goals>
                        <configuration>
                            <schemaLanguage>WSDL</schemaLanguage>
                            <files>
                                <file>test-resources/wsdl/AddNumbers.wsdl</file>
                            </files>
                            <outputDirectory>${project.build.directory}/generated-test-sources/jaxb/AddNumbers</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>build-repo</id>
                        <phase>test-compile</phase>
                        <configuration>
                            <target unless="maven.test.skip">
                                <copy toDir="target/test-classes/">
                                    <fileset dir="test-resources/">
                                        <include name="**/*.properties" />
                                        <include name="**/axis2.xml" />
                                    </fileset>
                                </copy>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.axis2</groupId>
                <artifactId>axis2-repo-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>create-test-repository</goal>
                        </goals>
                        <configuration>
                            <axis2xml>../kernel/conf/axis2.xml</axis2xml>
                            <configurationDirectory>conf</configurationDirectory>
                            <outputDirectory>${project.build.directory}/repository</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <inherited>true</inherited>
                <configuration>
                    <forkMode>once</forkMode>
                    <argLine>${argLine} -Xms256m -Xmx512m --add-opens java.base/java.net=ALL-UNNAMED</argLine>
                    <!-- Enable the next 2 lines if you want to attach a debugger
                    <argLine>-Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,server=y,suspend=y,address=5006</argLine>-->
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                    <systemProperties>
                        <!--<property>
                            <name>OASISCatalogManager.catalog.debug.level</name>
                            <value>9999</value>
                        </property>-->
                        <property>
                            <name>build.repository</name>
                            <value>./target/test-classes</value>
                        </property>
                        <property>
                            <name>jakarta.xml.soap.MessageFactory</name>
                            <value>org.apache.axis2.saaj.MessageFactoryImpl</value>
                        </property>
                        <property>
                            <name>jakarta.xml.soap.SOAPFactory</name>
                            <value>org.apache.axis2.saaj.SOAPFactoryImpl</value>
                        </property>
                        <property>
                            <name>jakarta.xml.soap.SOAPConnectionFactory</name>
                            <value>org.apache.axis2.saaj.SOAPConnectionFactoryImpl</value>
                        </property>
                        <property>
                            <name>jakarta.xml.soap.MetaFactory</name>
                            <value>org.apache.axis2.saaj.SAAJMetaFactoryImpl</value>
                        </property>
                        <!-- Need this for the client side to pickup an axis2.xml to configure SoapMessageMUProviderChecker -->
                        <property>
                            <name>org.apache.axis2.jaxws.config.path</name>
                            <value>./target/test-classes/axis2.xml</value>
                        </property>
                        <property>
                            <name>org.apache.axis2.jaxws.repo.path</name>
                            <value>./target/repository</value>
                        </property>
                        <!-- Prevent Mac OS X from showing an icon in the dock during the test run -->
                        <property>
                            <name>java.awt.headless</name>
                            <value>true</value>
                        </property>
                    </systemProperties>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
