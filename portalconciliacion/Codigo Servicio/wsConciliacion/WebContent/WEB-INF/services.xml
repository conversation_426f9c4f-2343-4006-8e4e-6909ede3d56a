<?xml version="1.0" encoding="UTF-8"?>
<serviceGroup>
    <service name="WsConciliacion" scope="application">
        <description>
            Servicio Web de Conciliación - Migrado de Axis 1.4 a Axis 2.0.0
        </description>
        
        <!-- Service Class -->
        <parameter name="ServiceClass">com.coppel.wsconciliacion.servicioweb.WsConciliacion</parameter>
        
        <!-- Operations -->
        <operation name="generarConciliacion">
            <messageReceiver class="org.apache.axis2.jaxws.server.JAXWSMessageReceiver"/>
        </operation>
        
        <operation name="reenviarCorreo">
            <messageReceiver class="org.apache.axis2.jaxws.server.JAXWSMessageReceiver"/>
        </operation>
        
        <operation name="obtenerUltimaFecha">
            <messageReceiver class="org.apache.axis2.jaxws.server.JAXWSMessageReceiver"/>
        </operation>
        
        <operation name="consultarEstadoConciliacion">
            <messageReceiver class="org.apache.axis2.jaxws.server.JAXWSMessageReceiver"/>
        </operation>
        
        <operation name="reenviarArchivoConnect">
            <messageReceiver class="org.apache.axis2.jaxws.server.JAXWSMessageReceiver"/>
        </operation>
        
        <!-- Schema and WSDL Configuration -->
        <parameter name="useOriginalwsdl">true</parameter>
        <parameter name="modifyUserWSDLPortAddress">true</parameter>
        
        <!-- Transport Configuration -->
        <transports>
            <transport>http</transport>
            <transport>https</transport>
        </transports>
    </service>
</serviceGroup>
