<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://servicioweb.wsconciliacion.coppel.com" xmlns:apachesoap="http://xml.apache.org/xml-soap" xmlns:impl="http://servicioweb.wsconciliacion.coppel.com" xmlns:intf="http://servicioweb.wsconciliacion.coppel.com" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
<!--WSDL created by Apache Axis version: 1.4
Built on Apr 22, 2006 (06:55:48 PDT)-->
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://servicioweb.wsconciliacion.coppel.com" xmlns="http://www.w3.org/2001/XMLSchema">
   <element name="reenviarArchivoConnect">
    <complexType>
     <sequence>
      <element name="peticion" type="xsd:int"/>
     </sequence>
    </complexType>
   </element>
   <element name="reenviarArchivoConnectResponse">
    <complexType>
     <sequence>
      <element name="reenviarArchivoConnectReturn" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="generarConciliacion">
    <complexType/>
   </element>
   <element name="generarConciliacionResponse">
    <complexType>
     <sequence>
      <element name="generarConciliacionReturn" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="obtenerUltimaFecha">
    <complexType/>
   </element>
   <element name="obtenerUltimaFechaResponse">
    <complexType>
     <sequence>
      <element name="obtenerUltimaFechaReturn" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="reenviarCorreo">
    <complexType>
     <sequence>
      <element name="peticion" type="xsd:int"/>
     </sequence>
    </complexType>
   </element>
   <element name="reenviarCorreoResponse">
    <complexType>
     <sequence>
      <element name="reenviarCorreoReturn" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="consultarEstadoConciliacion">
    <complexType/>
   </element>
   <element name="consultarEstadoConciliacionResponse">
    <complexType>
     <sequence>
      <element name="consultarEstadoConciliacionReturn" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="reenviarCorreoRequest">

      <wsdl:part element="impl:reenviarCorreo" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="consultarEstadoConciliacionResponse">

      <wsdl:part element="impl:consultarEstadoConciliacionResponse" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="generarConciliacionResponse">

      <wsdl:part element="impl:generarConciliacionResponse" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="reenviarArchivoConnectResponse">

      <wsdl:part element="impl:reenviarArchivoConnectResponse" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="reenviarCorreoResponse">

      <wsdl:part element="impl:reenviarCorreoResponse" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="consultarEstadoConciliacionRequest">

      <wsdl:part element="impl:consultarEstadoConciliacion" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="generarConciliacionRequest">

      <wsdl:part element="impl:generarConciliacion" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="reenviarArchivoConnectRequest">

      <wsdl:part element="impl:reenviarArchivoConnect" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="obtenerUltimaFechaRequest">

      <wsdl:part element="impl:obtenerUltimaFecha" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:message name="obtenerUltimaFechaResponse">

      <wsdl:part element="impl:obtenerUltimaFechaResponse" name="parameters">

      </wsdl:part>

   </wsdl:message>

   <wsdl:portType name="WsConciliacion">

      <wsdl:operation name="reenviarArchivoConnect">

         <wsdl:input message="impl:reenviarArchivoConnectRequest" name="reenviarArchivoConnectRequest">

       </wsdl:input>

         <wsdl:output message="impl:reenviarArchivoConnectResponse" name="reenviarArchivoConnectResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="generarConciliacion">

         <wsdl:input message="impl:generarConciliacionRequest" name="generarConciliacionRequest">

       </wsdl:input>

         <wsdl:output message="impl:generarConciliacionResponse" name="generarConciliacionResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="obtenerUltimaFecha">

         <wsdl:input message="impl:obtenerUltimaFechaRequest" name="obtenerUltimaFechaRequest">

       </wsdl:input>

         <wsdl:output message="impl:obtenerUltimaFechaResponse" name="obtenerUltimaFechaResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="reenviarCorreo">

         <wsdl:input message="impl:reenviarCorreoRequest" name="reenviarCorreoRequest">

       </wsdl:input>

         <wsdl:output message="impl:reenviarCorreoResponse" name="reenviarCorreoResponse">

       </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="consultarEstadoConciliacion">

         <wsdl:input message="impl:consultarEstadoConciliacionRequest" name="consultarEstadoConciliacionRequest">

       </wsdl:input>

         <wsdl:output message="impl:consultarEstadoConciliacionResponse" name="consultarEstadoConciliacionResponse">

       </wsdl:output>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="WsConciliacionSoapBinding" type="impl:WsConciliacion">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="reenviarArchivoConnect">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="reenviarArchivoConnectRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="reenviarArchivoConnectResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="generarConciliacion">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="generarConciliacionRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="generarConciliacionResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="obtenerUltimaFecha">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="obtenerUltimaFechaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="obtenerUltimaFechaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="reenviarCorreo">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="reenviarCorreoRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="reenviarCorreoResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

      <wsdl:operation name="consultarEstadoConciliacion">

         <wsdlsoap:operation soapAction=""/>

         <wsdl:input name="consultarEstadoConciliacionRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="consultarEstadoConciliacionResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="WsConciliacionService">

      <wsdl:port binding="impl:WsConciliacionSoapBinding" name="WsConciliacion">

         <wsdlsoap:address location="http://tempuri.org/ws-conciliacion/services/WsConciliacion"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
