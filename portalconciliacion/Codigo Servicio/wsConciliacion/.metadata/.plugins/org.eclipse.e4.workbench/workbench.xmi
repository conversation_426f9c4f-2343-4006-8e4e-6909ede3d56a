<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_Qqvhgc6yEeaBOvrLLHWY2A" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_QqwIkM6yEeaBOvrLLHWY2A" bindingContexts="_QqwIks6yEeaBOvrLLHWY2A">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <tags>ModelMigrationProcessor.001</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_QqwIkM6yEeaBOvrLLHWY2A" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_Q-YPMs6yEeaBOvrLLHWY2A" label="%trimmedwindow.label.eclipseSDK" x="16" y="29" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1483121314303"/>
    <tags>topLevel</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_Q-YPMs6yEeaBOvrLLHWY2A" selectedElement="_Q-Y2Qc6yEeaBOvrLLHWY2A" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_Q-Y2QM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_RLNxUM6yEeaBOvrLLHWY2A">
        <children xsi:type="advanced:Perspective" xmi:id="_RLNxUM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.j2ee.J2EEPerspective" selectedElement="_RLNxUc6yEeaBOvrLLHWY2A" label="Java EE" iconURI="platform:/plugin/org.eclipse.jst.j2ee.ui/icons/full/cview16/j2ee_perspective.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.jst.j2ee.J2eeMainActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.wst.server.ui.ServersView</tags>
          <tags>persp.viewSC:org.eclipse.datatools.connectivity.DataSourceExplorerNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.BookmarkView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.viewSC:org.eclipse.wst.common.snippets.internal.ui.SnippetsView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.AllMarkersView</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.jpa.ui.wizard.newJpaProject</tags>
          <tags>persp.perspSC:org.eclipse.jpt.ui.jpaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.ui.resourcePerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.web.ui.webDevPerspective</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.EarProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.project.facet.WebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.project.facet.EjbProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.jca.ui.internal.wizard.ConnectorProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.appclient.AppClientProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.web.ui.internal.wizards.SimpleWebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newJpaProject</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.internal.wizard.AddServletWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddSessionBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddMessageDrivenBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newEntity</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ws.creation.ui.wizard.serverwizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.actionSet:org.eclipse.wst.server.ui.internal.webbrowser.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.actionSet:org.eclipse.wst.ws.explorer.explorer</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_RLNxUc6yEeaBOvrLLHWY2A" selectedElement="_RLNxUs6yEeaBOvrLLHWY2A" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_RLNxUs6yEeaBOvrLLHWY2A" elementId="topLeft" containerData="2500" selectedElement="_RLNxU86yEeaBOvrLLHWY2A">
              <children xsi:type="advanced:Placeholder" xmi:id="_RLNxU86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_RKFI4M6yEeaBOvrLLHWY2A"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_RLNxVM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_RKGXAM6yEeaBOvrLLHWY2A"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_RLNxVc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_RKGXAc6yEeaBOvrLLHWY2A"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_RLNxVs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.PackagesView" toBeRendered="false" ref="_RLI40M6yEeaBOvrLLHWY2A"/>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_RLNxV86yEeaBOvrLLHWY2A" containerData="7500">
              <children xsi:type="basic:PartSashContainer" xmi:id="_RLNxWM6yEeaBOvrLLHWY2A" containerData="7000" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxWc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.editorss" containerData="7000" ref="_RJ7-8M6yEeaBOvrLLHWY2A"/>
                <children xsi:type="basic:PartStack" xmi:id="_RLNxWs6yEeaBOvrLLHWY2A" elementId="topRight" containerData="3000" selectedElement="_RLNxW86yEeaBOvrLLHWY2A">
                  <children xsi:type="advanced:Placeholder" xmi:id="_RLNxW86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ContentOutline" ref="_RLLVEc6yEeaBOvrLLHWY2A"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_RLNxXM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_RLL8IM6yEeaBOvrLLHWY2A"/>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_RLNxXc6yEeaBOvrLLHWY2A" elementId="bottomRight" containerData="3000" selectedElement="_RLNxXs6yEeaBOvrLLHWY2A">
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxXs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.AllMarkersView" ref="_RLI40c6yEeaBOvrLLHWY2A"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxX86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.PropertySheet" ref="_RLJf4M6yEeaBOvrLLHWY2A"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxYM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.ui.ServersView" ref="_RLJf4c6yEeaBOvrLLHWY2A"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxYc6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" ref="_RLKG8M6yEeaBOvrLLHWY2A"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxYs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" ref="_RLKG8c6yEeaBOvrLLHWY2A"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxY86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ProblemView" toBeRendered="false" ref="_RLKuAM6yEeaBOvrLLHWY2A"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxZM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.TaskList" toBeRendered="false" ref="_RLKuAc6yEeaBOvrLLHWY2A"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxZc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_RLKuAs6yEeaBOvrLLHWY2A"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxZs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_RLKuA86yEeaBOvrLLHWY2A"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxZ86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_RLKuBM6yEeaBOvrLLHWY2A"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RLNxaM6yEeaBOvrLLHWY2A" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_RLLVEM6yEeaBOvrLLHWY2A"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_Q-Y2Qc6yEeaBOvrLLHWY2A" elementId="stickyFolderRight" containerData="2500" selectedElement="_Q-Y2Q86yEeaBOvrLLHWY2A">
        <tags>active</tags>
        <children xsi:type="advanced:Placeholder" xmi:id="_Q-Y2Qs6yEeaBOvrLLHWY2A" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_Q-XBEM6yEeaBOvrLLHWY2A"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_Q-Y2Q86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.internal.introview" ref="_Q-YPMM6yEeaBOvrLLHWY2A"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_Q-Y2RM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_Q-YPMc6yEeaBOvrLLHWY2A"/>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_Q-XBEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Q-YPMM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;presentation currentPage=&quot;root&quot; restore=&quot;true&quot;/>&#xD;&#xA;&lt;standbyPart/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_RYsMoM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_RYsMoc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.internal.introview"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Q-YPMc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_RJ7-8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_RJ7-8c6yEeaBOvrLLHWY2A" elementId="org.eclipse.e4.primaryDataStack">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RKFI4M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; currentWorkingSetName=&quot;Aggregate for window 1483121314303&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_RNr2gM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_RNr2gc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RKGXAM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RKGXAc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLI40M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.PackagesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Java Browsing</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLI40c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.AllMarkersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.allSeverityField&quot; categoryGroup=&quot;org.eclipse.ui.ide.type&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.allMarkersGenerator&quot; partName=&quot;Markers&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.allSeverityField=&quot;250&quot; org.eclipse.ui.ide.locationField=&quot;75&quot; org.eclipse.ui.ide.markerType=&quot;75&quot; org.eclipse.ui.ide.pathField=&quot;100&quot; org.eclipse.ui.ide.resourceField=&quot;75&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.allSeverityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_RUawoM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.AllMarkersView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_RUawoc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.AllMarkersView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLJf4M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLJf4c6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Server</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLKG8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Data Source Explorer" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Data Management</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLKG8c6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLKuAM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLKuAc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLKuAs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLKuA86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLKuBM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLLVEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLLVEc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_RTEFwM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_RTEFwc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ContentOutline"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RLL8IM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
    </sharedElements>
    <trimBars xmi:id="_Q-fj8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.main.toolbar">
      <children xsi:type="menu:ToolBar" xmi:id="_RAB1AM6yEeaBOvrLLHWY2A" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_RACcEM6yEeaBOvrLLHWY2A" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RADDIM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_lmMAsM6yEeaCiuxCQIAjyQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_Qra2986yEeaBOvrLLHWY2A"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RADDIc6yEeaBOvrLLHWY2A" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_RADDIs6yEeaBOvrLLHWY2A" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RLqdQM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RMQ6MM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RLwj4M6yEeaBOvrLLHWY2A" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RMNP0M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RLjIgM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.ws.explorer.explorer">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RADDI86yEeaBOvrLLHWY2A" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_RADDJM6yEeaBOvrLLHWY2A" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RADqMM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_lmOc8M6yEeaCiuxCQIAjyQ" elementId="org.eclipse.ui.window.pinEditor" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_QrXzuc6yEeaBOvrLLHWY2A"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RADqMc6yEeaBOvrLLHWY2A" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_RADqMs6yEeaBOvrLLHWY2A" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RADqM86yEeaBOvrLLHWY2A" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_RADqNM6yEeaBOvrLLHWY2A" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RADqNc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_RH7MwM6yEeaBOvrLLHWY2A" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_RH8a4M6yEeaBOvrLLHWY2A" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_RIGy8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.trim.status" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_RIM5kM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_RIWDgM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_RIf0gM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_RIqzoM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.trim.vertical1" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_RbMuEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_RIrasM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.trim.vertical2" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_fbrmQM6yEeaBOvrLLHWY2A" elementId="topRight(org.eclipse.jst.j2ee.J2EEPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_fbyT8M6yEeaBOvrLLHWY2A" elementId="bottomRight(org.eclipse.jst.j2ee.J2EEPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_fb620M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.editorss(org.eclipse.jst.j2ee.J2EEPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
      </children>
    </trimBars>
  </children>
  <bindingTables xmi:id="_QqwIkc6yEeaBOvrLLHWY2A" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_QqwIks6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QthIuc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+F2" command="_QrUJSM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qthvwc6yEeaBOvrLLHWY2A" keySequence="CTRL+INSERT" command="_QrZByc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qthvw86yEeaBOvrLLHWY2A" keySequence="CTRL+A" command="_QrZBxc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtjk-M6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+I" command="_QrXzvM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtjk-c6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+F1" command="_QrQe5c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtjlAc6yEeaBOvrLLHWY2A" keySequence="CTRL+SPACE" command="_QrZB986yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMBc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+SPACE" command="_QrWlg86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtnPUs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+L" command="_QrXz286yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtnPVc6yEeaBOvrLLHWY2A" keySequence="CTRL+1" command="_QrUJZs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtnPWs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+F3" command="_QrbeNM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtn2Zc6yEeaBOvrLLHWY2A" keySequence="CTRL+F10" command="_QrZB5c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV8M6yEeaBOvrLLHWY2A" keySequence="CTRL+Z" command="_QrRtJs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV8c6yEeaBOvrLLHWY2A" keySequence="CTRL+Y" command="_QrVXhc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV8s6yEeaBOvrLLHWY2A" keySequence="CTRL+X" command="_QraP686yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV9c6yEeaBOvrLLHWY2A" keySequence="CTRL+V" command="_QrYats6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukEs6yEeaBOvrLLHWY2A" keySequence="SHIFT+INSERT" command="_QrYats6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAX86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+D" command="_QrVXf86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxnYs6yEeaBOvrLLHWY2A" keySequence="SHIFT+DEL" command="_QraP686yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxnZs6yEeaBOvrLLHWY2A" keySequence="ALT+PAGE_UP" command="_QrZB886yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtxna86yEeaBOvrLLHWY2A" keySequence="CTRL+C" command="_QrZByc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtxncc6yEeaBOvrLLHWY2A" keySequence="ALT+PAGE_DOWN" command="_QrUwc86yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtdeUM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_QrehXM6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtghoM6yEeaBOvrLLHWY2A" keySequence="CTRL+I" command="_QrZB1M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qti95c6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F" command="_QrVXfs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMAc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_UP" command="_QrZpBc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzFc6yEeaBOvrLLHWY2A" keySequence="CTRL+/" command="_QrRF_s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzGc6yEeaBOvrLLHWY2A" keySequence="CTRL+7" command="_QrRF_s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzHc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+O" command="_QrQe5M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtlaKM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+P" command="_QrTiVc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtlaL86yEeaBOvrLLHWY2A" keySequence="CTRL+T" command="_QrZB2s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEic6yEeaBOvrLLHWY2A" keySequence="CTRL+F3" command="_QrZo5s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtprl86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+/" command="_QrXzwM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5s86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+M" command="_Qra2-s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5v86yEeaBOvrLLHWY2A" keySequence="CTRL+O" command="_QrS7K86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtrgzM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+U" command="_QrV-js6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9Cs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+ARROW_UP" command="_QrUwVM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9Dc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+B" command="_Qra28M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9EM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_DOWN" command="_QrZBwc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukE86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_QrXMpM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukF86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+C" command="_QrRF_s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvLIM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+\" command="_QrbeP86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvyNs6yEeaBOvrLLHWY2A" keySequence="CTRL+2 M" command="_QrS7Mc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvyQs6yEeaBOvrLLHWY2A" keySequence="CTRL+2 R" command="_QrSUFc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvyRM6yEeaBOvrLLHWY2A" keySequence="CTRL+2 L" command="_QrTiRc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtwZQc6yEeaBOvrLLHWY2A" keySequence="CTRL+2 F" command="_QrV-is6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAU86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_LEFT" command="_QrbeBs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAVs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_QrbeOM6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QthIsM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" bindingContext="_QrehW86yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QthIsc6yEeaBOvrLLHWY2A" keySequence="CTRL+I" command="_QrSULM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qti96M6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F" command="_Qra3DM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMA86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_UP" command="_QrYa4M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzGM6yEeaBOvrLLHWY2A" keySequence="CTRL+/" command="_Qra3HM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzG86yEeaBOvrLLHWY2A" keySequence="CTRL+7" command="_Qra3HM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzHs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+O" command="_QrV-jc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtlaK86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+P" command="_QrV-oc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtlaMM6yEeaBOvrLLHWY2A" keySequence="CTRL+T" command="_QrbeE86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEiM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+T" command="_QrWlt86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEis6yEeaBOvrLLHWY2A" keySequence="CTRL+F3" command="_QrVXYs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtprmM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+/" command="_QrYa686yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5ts6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+M" command="_QrXzv86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgws6yEeaBOvrLLHWY2A" keySequence="CTRL+O" command="_QrbeIM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgz86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+U" command="_QrRtA86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9C86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+ARROW_UP" command="_QrTiSs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9Es6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_DOWN" command="_Qra3Ec6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukFM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_QrQe4s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukGs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+C" command="_Qra3HM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvLIc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+\" command="_QrTiRM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvyQ86yEeaBOvrLLHWY2A" keySequence="CTRL+2 R" command="_Qra3Ls6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtwZQM6yEeaBOvrLLHWY2A" keySequence="CTRL+2 L" command="_QrVXb86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtwZQs6yEeaBOvrLLHWY2A" keySequence="CTRL+2 F" command="_QrUwgM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAVM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_LEFT" command="_QrZo1M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAV86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_Qra3Jc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxnYM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+E" command="_QrYa3M6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QthIss6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_QrehZM6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QthIs86yEeaBOvrLLHWY2A" keySequence="CTRL+I" command="_QrRF_86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtjk8M6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F" command="_QrTiR86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtjk8s6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+>" command="_QrTiOM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMBM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_UP" command="_QrXzoM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtlaLM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+P" command="_QrWlhs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmBMc6yEeaBOvrLLHWY2A" keySequence="F3" command="_QrUJUs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtprmc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+/" command="_QrUwWM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgw86yEeaBOvrLLHWY2A" keySequence="CTRL+O" command="_QraQHc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9Cc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+A" command="_QrWlqc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9DM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+ARROW_UP" command="_QrbeBc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9E86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_DOWN" command="_QrS7Ls6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukFc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_Qra3Dc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukG86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+C" command="_QrSULc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvLIs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+\" command="_QraP_s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAVc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_LEFT" command="_QrWlsM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAWM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_QrSUG86yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QthItM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.textEditorScope" bindingContext="_QrehWc6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QthItc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+J" command="_QraQA86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QthIuM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_QrXzxc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtiW0c6yEeaBOvrLLHWY2A" keySequence="CTRL+ARROW_UP" command="_Qra29c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtjk886yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+ARROW_UP" command="_QrVXks6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtjk9M6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+INSERT" command="_QrUwcM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtjk_86yEeaBOvrLLHWY2A" keySequence="ALT+ARROW_DOWN" command="_QraP7s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMAM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+A" command="_QrZo786yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMBs6yEeaBOvrLLHWY2A" keySequence="CTRL+ARROW_DOWN" command="_Qra28c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmoQs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+Q" command="_QrUwbs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmoQ86yEeaBOvrLLHWY2A" keySequence="CTRL+NUMPAD_DIVIDE" command="_QrVXd86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmoRM6yEeaBOvrLLHWY2A" keySequence="CTRL+NUMPAD_MULTIPLY" command="_QrZB-M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmoRs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_QrZB7s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtnPUM6yEeaBOvrLLHWY2A" keySequence="CTRL+NUMPAD_ADD" command="_QrUJVs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtnPUc6yEeaBOvrLLHWY2A" keySequence="CTRL+NUMPAD_SUBTRACT" command="_QrV-es6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtnPW86yEeaBOvrLLHWY2A" keySequence="CTRL+K" command="_QrbeGc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtn2YM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+K" command="_QrXMls6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtn2Yc6yEeaBOvrLLHWY2A" keySequence="CTRL+J" command="_QrZB6c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtn2Ys6yEeaBOvrLLHWY2A" keySequence="CTRL+L" command="_QraP986yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtn2ZM6yEeaBOvrLLHWY2A" keySequence="INSERT" command="_QrXz1c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtn2Zs6yEeaBOvrLLHWY2A" keySequence="CTRL+F10" command="_QrZo986yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtn2aM6yEeaBOvrLLHWY2A" keySequence="SHIFT+CR" command="_QrZB9c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtn2ac6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+CR" command="_QrRF8c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtn2as6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+X" command="_QrWlh86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtodcM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+Y" command="_QrZpBs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtodcc6yEeaBOvrLLHWY2A" keySequence="END" command="_QrXMpc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtodcs6yEeaBOvrLLHWY2A" keySequence="HOME" command="_QrZo6c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtoddM6yEeaBOvrLLHWY2A" keySequence="F2" command="_QrXz0s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtoddc6yEeaBOvrLLHWY2A" keySequence="ALT+/" command="_QrcFE86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttWAs6yEeaBOvrLLHWY2A" keySequence="CTRL+HOME" command="_Qra3FM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttWA86yEeaBOvrLLHWY2A" keySequence="SHIFT+END" command="_QrQe786yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9Ac6yEeaBOvrLLHWY2A" keySequence="CTRL+DEL" command="_QrRtJc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9B86yEeaBOvrLLHWY2A" keySequence="ALT+ARROW_UP" command="_QrV-h86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9FM6yEeaBOvrLLHWY2A" keySequence="CTRL+BS" command="_QrSUK86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9Fc6yEeaBOvrLLHWY2A" keySequence="CTRL+END" command="_QrRtDM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9Fs6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+J" command="_QrVXhs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukFs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_QrUwZ86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAWs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+DEL" command="_QrSUIc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAXM6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+ARROW_DOWN" command="_QrV-jM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAYc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_QrbeM86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxnZc6yEeaBOvrLLHWY2A" keySequence="SHIFT+HOME" command="_QraQAM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtxnbc6yEeaBOvrLLHWY2A" keySequence="CTRL+ARROW_LEFT" command="_QrSUF86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtyOcM6yEeaBOvrLLHWY2A" keySequence="CTRL+D" command="_QrTiSc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtyOcc6yEeaBOvrLLHWY2A" keySequence="CTRL+ARROW_RIGHT" command="_Qra3Is6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QthIts6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.contexts.window" bindingContext="_QqwIk86yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QthIt86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_QrXzq86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qthvws6yEeaBOvrLLHWY2A" keySequence="ALT+ARROW_LEFT" command="_QrZo_s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QthvxM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+D P" command="_QrbeH86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qthvxc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+D O" command="_QrYauM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qthvxs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+D E" command="_QrTiRs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qthvx86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+D X" command="_QrUwac6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtiW0M6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+D R" command="_Qra3GM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtiW0s6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+D T" command="_QrZo3M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtiW086yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+D A" command="_QrV-dc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtiW1M6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+D J" command="_QrbeAc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtiW1c6yEeaBOvrLLHWY2A" keySequence="ALT+C" command="_QrXMm86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qti94c6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+D Q" command="_QraQF86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qti94s6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+G" command="_QrV-iM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtjk8c6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_QrRtAs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtjk9c6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+J" command="_QrcFFs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMB86yEeaBOvrLLHWY2A" keySequence="CTRL+B" command="_QrXzqs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMCs6yEeaBOvrLLHWY2A" keySequence="ALT+ARROW_RIGHT" command="_QrVXis6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMDM6yEeaBOvrLLHWY2A" keySequence="SHIFT+F5" command="_QrYaxM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzEs6yEeaBOvrLLHWY2A" keySequence="CTRL+U" command="_QrcFGs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzE86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+N" command="_QrXMws6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtlaIM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+R" command="_QrUwZc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtlaMs6yEeaBOvrLLHWY2A" keySequence="F3" command="_QrUwcc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmBM86yEeaBOvrLLHWY2A" keySequence="SHIFT+F2" command="_Qra3M86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmBNc6yEeaBOvrLLHWY2A" keySequence="ALT+F7" command="_QrZo-86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmBNs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+F7" command="_QrZB2M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmoQM6yEeaBOvrLLHWY2A" keySequence="CTRL+{" command="_QrZpDM6yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtmoQc6yEeaBOvrLLHWY2A" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_QtmoRc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_QrVXdc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtnPVs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+R" command="_QrTiPc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtnPV86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+W" command="_QrYa5s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtnPWM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+N" command="_QrRtI86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtnPWc6yEeaBOvrLLHWY2A" keySequence="CTRL+3" command="_QrbeNs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtn2Z86yEeaBOvrLLHWY2A" keySequence="CTRL+Q" command="_QrVXbM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtodc86yEeaBOvrLLHWY2A" keySequence="F2" command="_QrYa6s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEgM6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+P" command="_Qra3Bc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEhc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+U" command="_QrXzx86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEh86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+T" command="_QrUJUc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEi86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+L" command="_QrbeA86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtprkc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+M" command="_QrbeD86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtprk86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+T" command="_QrV-cs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtprlc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Z" command="_QrRF986yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtprms6yEeaBOvrLLHWY2A" keySequence="ALT+X" command="_QrRtIs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtqSoc6yEeaBOvrLLHWY2A" keySequence="CTRL+BREAK" command="_Qra3Ds6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtqSo86yEeaBOvrLLHWY2A" keySequence="F11" command="_QrRtJ86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtqSpM6yEeaBOvrLLHWY2A" keySequence="CTRL+F11" command="_QrVXkc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtqSq86yEeaBOvrLLHWY2A" keySequence="CTRL+-" command="_QrUwZM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5sM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+V" command="_QrYa1M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5u86yEeaBOvrLLHWY2A" keySequence="ALT+F5" command="_QrTiM86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5vM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+P" command="_QrZo_86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5vs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F12" command="_QrbeCM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtrgxM6yEeaBOvrLLHWY2A" keySequence="CTRL+F12" command="_QrZCAM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgxc6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+SHIFT+F12" command="_Qra3Js6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgxs6yEeaBOvrLLHWY2A" keySequence="CTRL+F9" command="_QrYasM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtrgyM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F9" command="_QrRtFM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgyc6yEeaBOvrLLHWY2A" keySequence="F4" command="_QrZo886yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrg0M6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+S" command="_QrYauc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH0s6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+O" command="_QrVXYc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH2M6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+SHIFT+M" command="_QrYa186yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH2c6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+T" command="_QrYa3c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH4c6yEeaBOvrLLHWY2A" keySequence="ALT+V" command="_QrZB6M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu486yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+SHIFT+T" command="_QrUwU86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu5c6yEeaBOvrLLHWY2A" keySequence="CTRL+N" command="_QrS7RM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu586yEeaBOvrLLHWY2A" keySequence="CTRL+W" command="_QrS7Qs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu6M6yEeaBOvrLLHWY2A" keySequence="CTRL+F4" command="_QrS7Qs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu6c6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+W" command="_QrbeMM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu6s6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F4" command="_QrbeMM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu686yEeaBOvrLLHWY2A" keySequence="CTRL+S" command="_QrZo7s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu7M6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+S" command="_QrTiQ86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu7c6yEeaBOvrLLHWY2A" keySequence="CTRL+P" command="_Qra2986yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu786yEeaBOvrLLHWY2A" keySequence="ALT+CR" command="_QrV-hM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu8c6yEeaBOvrLLHWY2A" keySequence="F5" command="_QrYaz86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV9s6yEeaBOvrLLHWY2A" keySequence="DEL" command="_QrRtDs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV986yEeaBOvrLLHWY2A" keySequence="CTRL+." command="_QrS7Kc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV-M6yEeaBOvrLLHWY2A" keySequence="CTRL+," command="_QrRtHc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV-c6yEeaBOvrLLHWY2A" keySequence="ALT+-" command="_QrUwWc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV-s6yEeaBOvrLLHWY2A" keySequence="F12" command="_QrVXYM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV-86yEeaBOvrLLHWY2A" keySequence="CTRL+M" command="_QrTiTs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV_M6yEeaBOvrLLHWY2A" keySequence="CTRL+F6" command="_QrSUJM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV_c6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F6" command="_QrZB7M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV_s6yEeaBOvrLLHWY2A" keySequence="CTRL+F7" command="_QrV-gM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttV_86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F7" command="_QrUJTM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttWAM6yEeaBOvrLLHWY2A" keySequence="CTRL+F8" command="_QrcFEc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttWAc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F8" command="_QrUJU86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttWBc6yEeaBOvrLLHWY2A" keySequence="CTRL+G" command="_QrS7MM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9AM6yEeaBOvrLLHWY2A" keySequence="CTRL+DEL" command="_QrUwfc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9As6yEeaBOvrLLHWY2A" keySequence="CTRL+_" command="_QrZpDM6yEeaBOvrLLHWY2A">
      <parameters xmi:id="_Qtt9A86yEeaBOvrLLHWY2A" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_Qtt9CM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+A" command="_QrUwdM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9F86yEeaBOvrLLHWY2A" keySequence="CTRL+H" command="_Qra3Fs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukHM6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_QraP586yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukHc6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+SHIFT+A" command="_QrXzzs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukHs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+H" command="_QrQe6s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukIM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+I" command="_QrTiMM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvLJM6yEeaBOvrLLHWY2A" keySequence="CTRL+E" command="_QrYasc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvLJc6yEeaBOvrLLHWY2A" keySequence="CTRL+=" command="_QrUJZc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvLJs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q Z" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtvLJ86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_QtvLKM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q Y" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtvLKc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_QtvLKs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q S" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtvLK86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_QtvLLM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+X P" command="_Qra3EM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvLLc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+X O" command="_QrTiU86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvLLs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+X E" command="_QrbePc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvLL86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q K" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtvLMM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_QtvLMc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+X M" command="_QrbeDM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvLMs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q V" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtvLM86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_QtvyMM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q B" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtvyMc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_QtvyMs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+X Q" command="_QrZpA86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvyM86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+X J" command="_Qra3CM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvyNM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+X A" command="_QrbeRc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvyNc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+X T" command="_QrV-gc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvyN86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q H" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtvyOM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_QtvyOc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q C" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtvyOs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_QtvyO86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q Q" command="_QrXMl86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvyPM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q X" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtvyPc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_QtvyPs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q O" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtvyP86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_QtvyQM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q L" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtvyQc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_QtwZQ86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q T" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtwZRM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_QtwZRs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q P" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtwZR86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_QtwZSc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q D" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtwZSs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_QtwZTM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q J" command="_QrXMl86yEeaBOvrLLHWY2A">
      <parameters xmi:id="_QtwZTc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_QtwZT86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+X R" command="_QraQCc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtwZUM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+X X" command="_QrZB286yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtwZUc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+C" command="_QrV-i86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAUs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+B" command="_QrVXZs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAWc6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+G" command="_QrXz3s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAW86yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+B" command="_QrYa3s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAXc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+E" command="_QrUJS86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAXs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+F" command="_QrbePM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxnYc6yEeaBOvrLLHWY2A" keySequence="SHIFT+DEL" command="_QrZB3s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxnY86yEeaBOvrLLHWY2A" keySequence="CTRL+F" command="_QrUwa86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxnbM6yEeaBOvrLLHWY2A" keySequence="CTRL+#" command="_QraP_c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtxnbs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+H" command="_QrXMk86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtxnb86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+G" command="_QrRtGM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtyOcs6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+H" command="_QrRtH86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtyOdM6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_QrbeCs6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QthIus6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_QrehY86yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QthvwM6yEeaBOvrLLHWY2A" keySequence="CTRL+INSERT" command="_QrUwas6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMCc6yEeaBOvrLLHWY2A" keySequence="ALT+ARROW_RIGHT" command="_QrXMo86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu4s6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+V" command="_QrXzss6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9Bs6yEeaBOvrLLHWY2A" keySequence="ALT+ARROW_UP" command="_QrUwUs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukEc6yEeaBOvrLLHWY2A" keySequence="SHIFT+INSERT" command="_QrXzss6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukGc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+C" command="_QrUwas6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtiW1s6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" bindingContext="_QrehXc6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtiW186yEeaBOvrLLHWY2A" keySequence="ALT+C" command="_QrXMnc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtjk-s6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+D" command="_QrTiOc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMC86yEeaBOvrLLHWY2A" keySequence="ALT+Q" command="_QrbeQs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzFM6yEeaBOvrLLHWY2A" keySequence="CTRL+/" command="_QrV-cM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtlaJ86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+P" command="_QrbeFM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEg86yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+R" command="_QraP6s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtprm86yEeaBOvrLLHWY2A" keySequence="ALT+X" command="_QrWluM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtqSoM6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+X" command="_QrYa4s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH3s6yEeaBOvrLLHWY2A" keySequence="ALT+S" command="_QrUwVs6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtiW2M6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_QrehgM6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_Qti94M6yEeaBOvrLLHWY2A" keySequence="ALT+C" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtjlAM6yEeaBOvrLLHWY2A" keySequence="ALT+B" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMCM6yEeaBOvrLLHWY2A" keySequence="ALT+D" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH2s6yEeaBOvrLLHWY2A" keySequence="ALT+L" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH286yEeaBOvrLLHWY2A" keySequence="ALT+N" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH3M6yEeaBOvrLLHWY2A" keySequence="ALT+P" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH3c6yEeaBOvrLLHWY2A" keySequence="ALT+R" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH386yEeaBOvrLLHWY2A" keySequence="ALT+S" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH4M6yEeaBOvrLLHWY2A" keySequence="ALT+T" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH4s6yEeaBOvrLLHWY2A" keySequence="ALT+V" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu4M6yEeaBOvrLLHWY2A" keySequence="ALT+W" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu4c6yEeaBOvrLLHWY2A" keySequence="ALT+Y" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9BM6yEeaBOvrLLHWY2A" keySequence="ALT+A" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtvLI86yEeaBOvrLLHWY2A" keySequence="ALT+G" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxnZM6yEeaBOvrLLHWY2A" keySequence="ALT+H" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxnZ86yEeaBOvrLLHWY2A" keySequence="ALT+E" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtxncs6yEeaBOvrLLHWY2A" keySequence="ALT+F" command="_QrWlgc6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_Qti9486yEeaBOvrLLHWY2A" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_QrehWs6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_Qti95M6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F" command="_QrVXfs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzHM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+O" command="_QrbeKc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzH86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+R" command="_QrXztM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtlaMc6yEeaBOvrLLHWY2A" keySequence="F3" command="_QrUJXM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmBMs6yEeaBOvrLLHWY2A" keySequence="SHIFT+F2" command="_QrZB0M6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_Qti95s6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_Qrehe86yEeaBOvrLLHWY2A">
    <bindings xmi:id="_Qti9586yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F" command="_QrZB6s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgwc6yEeaBOvrLLHWY2A" keySequence="CTRL+O" command="_QrbeEc6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_Qtjk9s6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" bindingContext="_QrehX86yEeaBOvrLLHWY2A">
    <bindings xmi:id="_Qtjk986yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+J" command="_QrS7Pc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtlaJs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+R" command="_QrRtCc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmBMM6yEeaBOvrLLHWY2A" keySequence="F3" command="_QrXMn86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtmBNM6yEeaBOvrLLHWY2A" keySequence="SHIFT+F2" command="_QrSUEM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEhs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+U" command="_QrWlk86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtprkM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+L" command="_QrS7Ks6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtprks6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+M" command="_QrV-kc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtprlM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+T" command="_QrQe7M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtprls6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Z" command="_QrQe8c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5ss6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+V" command="_QrUwd86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgy86yEeaBOvrLLHWY2A" keySequence="F4" command="_QrcFFc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH0c6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+S" command="_QrV-qM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH1c6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+O" command="_QrS7Ns6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttWBs6yEeaBOvrLLHWY2A" keySequence="CTRL+G" command="_QrXzvs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukH86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+H" command="_QrUwXc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukI86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+I" command="_QrZB086yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtwZRc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q T" command="_QrUwX86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtwZSM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q P" command="_QrZo186yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtwZS86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q D" command="_QrbeKM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtwZTs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+Q J" command="_QrZo586yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAUc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+C" command="_Qra3K86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxncM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+G" command="_QrYawc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtyOc86yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+H" command="_QrbePs6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_Qtjk-86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.serverViewScope" bindingContext="_QrehV86yEeaBOvrLLHWY2A">
    <bindings xmi:id="_Qtjk_M6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+D" command="_QrWlp86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEgc6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+P" command="_QraQH86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEgs6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+S" command="_QrZB-s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtpEhM6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+R" command="_Qra2_86yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_Qtjk_c6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_QrehU86yEeaBOvrLLHWY2A">
    <bindings xmi:id="_Qtjk_s6yEeaBOvrLLHWY2A" keySequence="ALT+ARROW_DOWN" command="_QraQCs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkMAs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_UP" command="_QrRF_M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtlaI86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+R" command="_QrRtKc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtn2Y86yEeaBOvrLLHWY2A" keySequence="INSERT" command="_QrZB_s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtodds6yEeaBOvrLLHWY2A" keySequence="CTRL+CR" command="_QrVXbs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgys6yEeaBOvrLLHWY2A" keySequence="F4" command="_QraQDM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgzc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+U" command="_QrUJVc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9Bc6yEeaBOvrLLHWY2A" keySequence="ALT+ARROW_UP" command="_QrS7Q86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtt9Ec6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+ARROW_DOWN" command="_QrbeO86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukEM6yEeaBOvrLLHWY2A" keySequence="SHIFT+INSERT" command="_QraP8M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukIc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+I" command="_QrZB_c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtwZUs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+C" command="_Qra3Cs6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtkMDc6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.debugging" bindingContext="_Qrehds6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtkMDs6yEeaBOvrLLHWY2A" keySequence="F6" command="_QrRF9c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzEM6yEeaBOvrLLHWY2A" keySequence="F7" command="_QrXMos6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzEc6yEeaBOvrLLHWY2A" keySequence="F8" command="_QrcFG86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtodec6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+3" command="_QrRF8M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtqSos6yEeaBOvrLLHWY2A" keySequence="CTRL+F2" command="_QrUJRs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtqSpc6yEeaBOvrLLHWY2A" keySequence="CTRL+R" command="_QrTiNs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtqSrM6yEeaBOvrLLHWY2A" keySequence="CTRL+F5" command="_QraP786yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu8M6yEeaBOvrLLHWY2A" keySequence="F5" command="_Qra2886yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtkzFs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_Qrehgc6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtkzF86yEeaBOvrLLHWY2A" keySequence="CTRL+/" command="_QrRF_s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtkzGs6yEeaBOvrLLHWY2A" keySequence="CTRL+7" command="_QrRF_s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukGM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+C" command="_QrRF_s6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtlaIc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" bindingContext="_Qrehes6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtlaIs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+R" command="_QrZo2c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5sc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+V" command="_QrVXg86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5tM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+M" command="_QrUJYs6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtlaJM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_QrehcM6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtlaJc6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+R" command="_QrRtKc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5tc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+M" command="_QrS7KM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5wM6yEeaBOvrLLHWY2A" keySequence="CTRL+O" command="_QrWluc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgzs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+U" command="_QrUJVc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtsH0M6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+S" command="_QrXMq86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtukIs6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+I" command="_QrZB_c6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAUM6yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+C" command="_Qra3Cs6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtlaKc6yEeaBOvrLLHWY2A" elementId="org.eclipse.core.runtime.xml" bindingContext="_Qrehas6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtlaKs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+P" command="_QrbeLM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxAYM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+D" command="_QrWlhc6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtlaLc6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_Qrehfs6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtlaLs6yEeaBOvrLLHWY2A" keySequence="CTRL+T" command="_QrVXbc6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtqSps6yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+M" command="_QrRF-s6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtqSp86yEeaBOvrLLHWY2A" keySequence="ALT+CTRL+N" command="_QrXz1M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu5M6yEeaBOvrLLHWY2A" keySequence="CTRL+N" command="_QrUwW86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu5s6yEeaBOvrLLHWY2A" keySequence="CTRL+W" command="_QrUJTs6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtnPU86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_Qrehb86yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtnPVM6yEeaBOvrLLHWY2A" keySequence="CTRL+1" command="_Qra3Fc6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_Qtodd86yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_QrehVs6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtodeM6yEeaBOvrLLHWY2A" keySequence="CTRL+CR" command="_QrYazs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtsu7s6yEeaBOvrLLHWY2A" keySequence="ALT+CR" command="_Qra2-M6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtqSqM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_Qrehd86yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtqSqc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+." command="_QrQe4M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtqSqs6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+," command="_QrbeJs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QttWBM6yEeaBOvrLLHWY2A" keySequence="CTRL+G" command="_QrUJSs6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtqSrc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" bindingContext="_QrfIY86yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtqSrs6yEeaBOvrLLHWY2A" keySequence="CTRL+F5" command="_QrZo-86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5uc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F5" command="_QrZB2M6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtqSr86yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.pagedesigner.editorContext" bindingContext="_Qrehdc6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtqSsM6yEeaBOvrLLHWY2A" keySequence="CTRL+F5" command="_QrZo-86yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5t86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F10" command="_QrUwcs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5uM6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F11" command="_QrZpAs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5us6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F5" command="_QrZB2M6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtq5vc6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F12" command="_QrbeAs6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_Qtrgx86yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+F9" command="_QrWlq86yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_Qtq5wc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_QrfIYM6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtrgwM6yEeaBOvrLLHWY2A" keySequence="CTRL+O" command="_QrWlmc6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtsH086yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_QrfIYc6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtsH1M6yEeaBOvrLLHWY2A" keySequence="CTRL+SHIFT+O" command="_QrWlmc6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_QtsH1s6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_Qrehgs6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QtsH186yEeaBOvrLLHWY2A" keySequence="F1" command="_QrS7Ms6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_Qtsu8s6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.console" bindingContext="_QrehWM6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_Qtsu886yEeaBOvrLLHWY2A" keySequence="CTRL+Z" command="_QrTiVs6yEeaBOvrLLHWY2A">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_QttV886yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_QrehUs6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_QttV9M6yEeaBOvrLLHWY2A" keySequence="CTRL+V" command="_QrRtHM6yEeaBOvrLLHWY2A"/>
    <bindings xmi:id="_QtxnaM6yEeaBOvrLLHWY2A" keySequence="CTRL+C" command="_QrYaus6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_Qtt9Ds6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_QrehYc6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_Qtt9D86yEeaBOvrLLHWY2A" keySequence="ALT+SHIFT+B" command="_Qra28M6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_Qtxnac6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_Qrehcc6yEeaBOvrLLHWY2A">
    <bindings xmi:id="_Qtxnas6yEeaBOvrLLHWY2A" keySequence="CTRL+C" command="_QrS7Qc6yEeaBOvrLLHWY2A"/>
  </bindingTables>
  <bindingTables xmi:id="_RJ9NEc6yEeaBOvrLLHWY2A" bindingContext="_RJ9NEM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ9NE86yEeaBOvrLLHWY2A" bindingContext="_RJ9NEs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ9NFc6yEeaBOvrLLHWY2A" bindingContext="_RJ9NFM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ90Ic6yEeaBOvrLLHWY2A" bindingContext="_RJ90IM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ90I86yEeaBOvrLLHWY2A" bindingContext="_RJ90Is6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ90Jc6yEeaBOvrLLHWY2A" bindingContext="_RJ90JM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ90J86yEeaBOvrLLHWY2A" bindingContext="_RJ90Js6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ90Kc6yEeaBOvrLLHWY2A" bindingContext="_RJ90KM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ-bMc6yEeaBOvrLLHWY2A" bindingContext="_RJ-bMM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ-bM86yEeaBOvrLLHWY2A" bindingContext="_RJ-bMs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ-bNc6yEeaBOvrLLHWY2A" bindingContext="_RJ-bNM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ-bN86yEeaBOvrLLHWY2A" bindingContext="_RJ-bNs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ-bOc6yEeaBOvrLLHWY2A" bindingContext="_RJ-bOM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ_CQc6yEeaBOvrLLHWY2A" bindingContext="_RJ_CQM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ_CQ86yEeaBOvrLLHWY2A" bindingContext="_RJ_CQs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ_CRc6yEeaBOvrLLHWY2A" bindingContext="_RJ_CRM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ_CR86yEeaBOvrLLHWY2A" bindingContext="_RJ_CRs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ_CSc6yEeaBOvrLLHWY2A" bindingContext="_RJ_CSM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ_CS86yEeaBOvrLLHWY2A" bindingContext="_RJ_CSs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ_pUc6yEeaBOvrLLHWY2A" bindingContext="_RJ_pUM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ_pU86yEeaBOvrLLHWY2A" bindingContext="_RJ_pUs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ_pVc6yEeaBOvrLLHWY2A" bindingContext="_RJ_pVM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RJ_pV86yEeaBOvrLLHWY2A" bindingContext="_RJ_pVs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKAQYc6yEeaBOvrLLHWY2A" bindingContext="_RKAQYM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKAQY86yEeaBOvrLLHWY2A" bindingContext="_RKAQYs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKAQZc6yEeaBOvrLLHWY2A" bindingContext="_RKAQZM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKAQZ86yEeaBOvrLLHWY2A" bindingContext="_RKAQZs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKAQac6yEeaBOvrLLHWY2A" bindingContext="_RKAQaM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKA3cc6yEeaBOvrLLHWY2A" bindingContext="_RKA3cM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKA3c86yEeaBOvrLLHWY2A" bindingContext="_RKA3cs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKA3dc6yEeaBOvrLLHWY2A" bindingContext="_RKA3dM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKA3d86yEeaBOvrLLHWY2A" bindingContext="_RKA3ds6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKA3ec6yEeaBOvrLLHWY2A" bindingContext="_RKA3eM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKBegc6yEeaBOvrLLHWY2A" bindingContext="_RKBegM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKBeg86yEeaBOvrLLHWY2A" bindingContext="_RKBegs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKBehc6yEeaBOvrLLHWY2A" bindingContext="_RKBehM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKBeh86yEeaBOvrLLHWY2A" bindingContext="_RKBehs6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKBeic6yEeaBOvrLLHWY2A" bindingContext="_RKBeiM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKCFkc6yEeaBOvrLLHWY2A" bindingContext="_RKCFkM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKCFk86yEeaBOvrLLHWY2A" bindingContext="_RKCFks6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKCFlc6yEeaBOvrLLHWY2A" bindingContext="_RKCFlM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKCFl86yEeaBOvrLLHWY2A" bindingContext="_RKCFls6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKCsoc6yEeaBOvrLLHWY2A" bindingContext="_RKCsoM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKCso86yEeaBOvrLLHWY2A" bindingContext="_RKCsos6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKCspc6yEeaBOvrLLHWY2A" bindingContext="_RKCspM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKCsp86yEeaBOvrLLHWY2A" bindingContext="_RKCsps6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKCsqc6yEeaBOvrLLHWY2A" bindingContext="_RKCsqM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKDTsc6yEeaBOvrLLHWY2A" bindingContext="_RKDTsM6yEeaBOvrLLHWY2A"/>
  <bindingTables xmi:id="_RKDTs86yEeaBOvrLLHWY2A" bindingContext="_RKDTss6yEeaBOvrLLHWY2A"/>
  <rootContext xmi:id="_QqwIks6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_QqwIk86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_QqwIlM6yEeaBOvrLLHWY2A" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_QrehUs6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View"/>
      <children xmi:id="_QrehU86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_QrehVM6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_QrehVs6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_QrehV86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_QrehWM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_QrehWc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_QrehWs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_QrehW86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" name="Editing JavaScript Source" description="Editing JavaScript Source Context">
          <children xmi:id="_QrehX86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" name="JavaScript View" description="JavaScript View Context"/>
        </children>
        <children xmi:id="_QrehXM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_QrehXc6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" name="Editing SQL" description="Editing SQL Context"/>
        <children xmi:id="_QrehZM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_QrehZc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.html.core.htmlsource" name="Editing HTML Source" description="Editing HTML Source"/>
          <children xmi:id="_QrehZs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_QrehZ86yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.jsp.core.jspsource" name="JSP Source" description="JSP Source"/>
          <children xmi:id="_QrehaM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_Qrehac6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_Qrehas6yEeaBOvrLLHWY2A" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_Qreha86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_QrehbM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
          <children xmi:id="_Qrehbc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.html.occurrences" name="HTML Source Occurrences" description="HTML Source Occurrences"/>
          <children xmi:id="_Qrehcs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_Qrehc86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_QrehdM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_Qrehes6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" name="Editing JSP Source" description="Editing JSP Source"/>
          <children xmi:id="_Qrehf86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_QrfIZM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.css.core.csssource" name="Editing CSS Source" description="Editing CSS Source"/>
          <children xmi:id="_QrfIZc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
        </children>
        <children xmi:id="_Qrehb86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_QrehcM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_Qrehdc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.pagedesigner.editorContext" name="Using Web Page Editor" description="Key binding context when using the web page editor"/>
        <children xmi:id="_Qrehe86yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_Qrehfc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsd.ui.text.editor.context" name="Editing XSD context"/>
        <children xmi:id="_Qrehgc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
        <children xmi:id="_Qrehgs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_QrfIYM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_QrfIYc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_QrfIY86yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" name="In Faces Config Editor" description="Key binding context when using the Faces Config Editor"/>
      </children>
      <children xmi:id="_QrehYM6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.schemaobjecteditor.schemaediting" name="Schema Object Editor" description="Schema Object Editor"/>
      <children xmi:id="_QrehYs6yEeaBOvrLLHWY2A" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_QrehY86yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_Qrehcc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_Qrehds6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_Qrehd86yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_QreheM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsl.debug.ui.context" name="XSLT Debugging" description="Context for debugging XSLT"/>
        <children xmi:id="_QrehfM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_Qrehfs6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_QrehgM6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_QrfIYs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
    </children>
    <children xmi:id="_QqwIlc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_QrehUM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsd.ui.editor.designView" name="XSD Editor Design View" description="XSD Editor Design View"/>
  <rootContext xmi:id="_QrehUc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_QrehVc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.wsdl.ui.editor.designView" name="WSDL Editor Design View" description="WSDL Editor Design View"/>
  <rootContext xmi:id="_QrehXs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_QrehYc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_Qrehbs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.wsdl.ui.editor.sourceView" name="WSDL Editor Source View" description="WSDL Editor Source View"/>
  <rootContext xmi:id="_Qrehec6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsd.ui.editor.sourceView" name="XSD Editor Source View" description="XSD Editor Source View"/>
  <rootContext xmi:id="_RJ9NEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_RJ9NEs6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.actionSet" name="Auto::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet"/>
  <rootContext xmi:id="_RJ9NFM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_RJ90IM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_RJ90Is6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_RJ90JM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_RJ90Js6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_RJ90KM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_RJ-bMM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_RJ-bMs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_RJ-bNM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_RJ-bNs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_RJ-bOM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_RJ_CQM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_RJ_CQs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_RJ_CRM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_RJ_CRs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation" name="Auto::org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation"/>
  <rootContext xmi:id="_RJ_CSM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet" name="Auto::org.eclipse.jst.j2ee.J2eeMainActionSet"/>
  <rootContext xmi:id="_RJ_CSs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_RJ_pUM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.java.actionSet" name="Auto::org.eclipse.mylyn.java.actionSet"/>
  <rootContext xmi:id="_RJ_pUs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.java.actionSet.browsing" name="Auto::org.eclipse.mylyn.java.actionSet.browsing"/>
  <rootContext xmi:id="_RJ_pVM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.doc.actionSet" name="Auto::org.eclipse.mylyn.doc.actionSet"/>
  <rootContext xmi:id="_RJ_pVs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_RKAQYM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_RKAQYs6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_RKAQZM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_RKAQZs6yEeaBOvrLLHWY2A" elementId="org.eclipse.rse.core.search.searchActionSet" name="Auto::org.eclipse.rse.core.search.searchActionSet"/>
  <rootContext xmi:id="_RKAQaM6yEeaBOvrLLHWY2A" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_RKA3cM6yEeaBOvrLLHWY2A" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_RKA3cs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_RKA3dM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_RKA3ds6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_RKA3eM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_RKBegM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_RKBegs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_RKBehM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_RKBehs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_RKBeiM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_RKCFkM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_RKCFks6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.wst.jsdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_RKCFlM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_RKCFls6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.JavaActionSet" name="Auto::org.eclipse.wst.jsdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_RKCsoM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.A_OpenActionSet" name="Auto::org.eclipse.wst.jsdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_RKCsos6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.CodingActionSet" name="Auto::org.eclipse.wst.jsdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_RKCspM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.SearchActionSet" name="Auto::org.eclipse.wst.jsdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_RKCsps6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_RKCsqM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_RKDTsM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.web.ui.wizardsActionSet" name="Auto::org.eclipse.wst.web.ui.wizardsActionSet"/>
  <rootContext xmi:id="_RKDTss6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.ws.explorer.explorer" name="Auto::org.eclipse.wst.ws.explorer.explorer"/>
  <descriptors xmi:id="_QvVtoM6yEeaBOvrLLHWY2A" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
  </descriptors>
  <descriptors xmi:id="_QvYw8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_QveQgM6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" label="Data Source Explorer" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_QvfeoM6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.plan.planView" label="Execution Plan" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.plan/icons/sqlplan.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_QvgFsM6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.result.resultView" label="SQL Results" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.result.ui/icons/sqlresult.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_QvgswM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Qvh64M6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Qvih8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_QvjJAM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_QvjwEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_QvkXIM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Qvk-MM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_QvllQM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_QvmMUM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_QvmzYM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_QvnacM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_QvoBgM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_QvoBgc6yEeaBOvrLLHWY2A" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QvookM6yEeaBOvrLLHWY2A" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_QvpPoM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Display" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_Qvp2sM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_QvqdwM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_QvsS8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_Qvs6AM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_Qvs6Ac6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_QvthEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_QvthEc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_QvuIIM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_QvuvMM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QvuvMc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_QvvWQM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_Qvv9UM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.ui.jpaStructureView" label="JPA Structure" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-structure.gif" tooltip="" category="JPA" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_QvwkYM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.ui.jpaDetailsView" label="JPA Details" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-details.gif" tooltip="" category="JPA" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_QvxLcM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.jsf.ui.component.ComponentTreeView" label="JSF Component Tree" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="JavaServer Faces" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_QvxygM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.jsf.ui.tagregistry.TagRegistryView" label="Tag Registry" iconURI="platform:/plugin/org.eclipse.jst.jsf.ui/icons/obj16/library_obj.gif" tooltip="" category="JavaServer Faces" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_Qvxygc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.ws.jaxws.ui.views.AnnotationsView" label="Annotation Properties" iconURI="platform:/plugin/org.eclipse.jst.ws.jaxws.ui/icons/eview16/prop_ps.gif" tooltip="" category="Web Services" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Web Services</tags>
  </descriptors>
  <descriptors xmi:id="_QvyZkM6yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.gif" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_QvzAoM6yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_QvzAoc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_Qv0OwM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.gif" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_Qv010M6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_Qv010c6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_Qv1c4M6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.gif" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_Qv2rAM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.gif" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_Qv3SEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_Qv35IM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_Qv4gMM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_Qv4gMc6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_Qv5HQM6yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.apidocs.rcp.views.apidocs" label="API Docs" iconURI="platform:/plugin/org.eclipse.recommenders.apidocs.rcp/icons/view16/apidocs.png" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_Qv5uUM6yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.models.rcp.views.projectCoordinates" label="Project Coordinates" iconURI="platform:/plugin/org.eclipse.recommenders.coordinates.rcp/icons/view16/depinsp.gif" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_Qv5uUc6yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.models.rcp.views.modelRepositories" label="Model Repositories" iconURI="platform:/plugin/org.eclipse.recommenders.models.rcp/icons/view16/depinsp.gif" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_Qv68cM6yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.models.rcp.views.dependencyOverview" label="Dependency Overview" iconURI="platform:/plugin/org.eclipse.recommenders.models.rcp/icons/view16/depinsp.gif" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_Qv68cc6yEeaBOvrLLHWY2A" elementId="org.eclipse.rse.shells.ui.view.commandsView" label="Remote Shell" iconURI="platform:/plugin/org.eclipse.rse.shells.ui/icons/full/cview16/commands_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_Qv7jgM6yEeaBOvrLLHWY2A" elementId="org.eclipse.rse.ui.view.systemView" label="Remote Systems" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_Qv8KkM6yEeaBOvrLLHWY2A" elementId="org.eclipse.rse.ui.view.teamView" label="Team" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/team_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_Qv8xoM6yEeaBOvrLLHWY2A" elementId="org.eclipse.rse.ui.view.systemTableView" label="Remote System Details" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_Qv8xoc6yEeaBOvrLLHWY2A" elementId="org.eclipse.rse.ui.view.SystemSearchView" label="Remote Search" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/obj16/system_search.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_Qv9YsM6yEeaBOvrLLHWY2A" elementId="org.eclipse.rse.ui.view.scratchpad.SystemScratchpadViewPart" label="Remote Scratchpad" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/scratchpad_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_Qv9Ysc6yEeaBOvrLLHWY2A" elementId="org.eclipse.rse.ui.view.monitorView" label="Remote Monitor" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_Qv9_wM6yEeaBOvrLLHWY2A" elementId="org.eclipse.search.SearchResultView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Qv-m0M6yEeaBOvrLLHWY2A" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_Qv_N4M6yEeaBOvrLLHWY2A" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_Qv_08M6yEeaBOvrLLHWY2A" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_Qv_08c6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_QwAcAM6yEeaBOvrLLHWY2A" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_QwBDEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwBqIM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwCRMM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_QwCRMc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwC4QM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwDfUM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwDfUc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwEGYM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwEtcM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwEtcc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwFUgM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwF7kM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwGioM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwGioc6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwHJsM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_QwHwwM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_QwIX0M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.TypeHierarchy" label="Hierarchy" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/class_hi.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_QwJl8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.PackageExplorer" label="Script Explorer" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/package.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_QwKNAM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/call_hierarchy.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_QwKNAc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/source.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_QwK0EM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.JavadocView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/javadoc.gif" tooltip="JavaScript Documentation" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_QwK0Ec6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_QwLbIM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_QwMCMM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_QwMpQM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.views.XPathView" label="XPath" iconURI="platform:/plugin/org.eclipse.wst.xml.xpath.ui/icons/full/xpath.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_QwNQUM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsl.jaxp.debug.ui.resultview" label="Result" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_QwNQUc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsl.ui.view.outline" label="Stylesheet Model" iconURI="platform:/plugin/org.eclipse.wst.xsl.ui/icons/full/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_QrQe4M6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe4c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe4s6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe486yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.eclipselink.ui.upgradeToEclipseLinkMappingFile" commandName="Upgrade to EclipseLink Mapping File" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe5M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe5c6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_QrOpt86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe5s6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.discoveryWizardCommand" commandName="%command.name" description="%command.description" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe586yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe6M6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.connectivity.commands.import" commandName="Import Profiles Command" description="Command to import connection profiles" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe6c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe6s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe686yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_QrOpsM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe7M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe7c6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe7s6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe786yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrQe8c6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.debug.ui.script.opensource" commandName="Open Source" description="Shows the JavaScript source for the selected script element" category="_QrPQy86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF8c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF8s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF886yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.JavaPerspective" commandName="JavaScript" description="Show the JavaScript perspective" category="_QrOpsM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF9M6yEeaBOvrLLHWY2A" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_QrOpsM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF9c6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF9s6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF986yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF-M6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrRF-c6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_QrRF-s6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF-86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF_M6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF_c6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF_s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRF_86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtAM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtAc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtAs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_QrPQ1c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtA86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtBM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtBc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtBs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jaxb.ui.generateJaxbClasses" commandName="JAXB Classes..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtB86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtCM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtCc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtCs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtC86yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.attachProfileAction" commandName="Set Connection Information" category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtDM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtDc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtDs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtD86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeMapAs" commandName="Map As" category="_QrOpv86yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrRtEc6yEeaBOvrLLHWY2A" elementId="specifiedPersistentAttributeMappingKey" name="specified mapping key" optional="false"/>
    <parameters xmi:id="_QrRtEs6yEeaBOvrLLHWY2A" elementId="defaultPersistentAttributeMappingKey" name="default mapping key" optional="false"/>
  </commands>
  <commands xmi:id="_QrRtE86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtFM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtFc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.CreateTag" commandName="Create Tag..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtFs6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtF86yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXml" commandName="Add Attribute to XML" category="_QrOpv86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtGM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtGc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrRtGs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_QrRtG86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_QrRtHM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewPaste" commandName="Paste Repository Path or URI" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtHc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtHs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtH86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtIM6yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtIc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtIs6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.cutcolumn" commandName="Cut" category="_QrOpsc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtI86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtJM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtJc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtJs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtJ86yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtKM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.eclipselink.ui.generateDynamicEntities" commandName="Generate Dynamic Entities from Tables..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrRtKc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.external.javadoc" commandName="Open External JSDoc" description="Open the JSDoc of the selected element in an external browser" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUEc6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUEs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_QrPQ1c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUE86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUFM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUFc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUFs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.newEditor" commandName="New Editor" description="Open another editor on the active editor's input" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUF86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUGM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_QrPQ0c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUGc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUGs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUG86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUHM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_QrPQyc6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrSUHc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_QrSUHs6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUH86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUIM6yEeaBOvrLLHWY2A" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUIc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUIs6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_QrPQzM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUI86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUJM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUJc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUJs6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Annotations" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUJ86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUKM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUKc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUKs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUK86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSULM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.indent" commandName="Indent Line" description="Indents the current line" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSULc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSULs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUL86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUMM6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_QrPQ0M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUMc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUMs6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrSUM86yEeaBOvrLLHWY2A" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7IM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_QrOpuc6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrS7Ic6yEeaBOvrLLHWY2A" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_QrS7Is6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7I86yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_QrPQzM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7JM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Jc6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_QrOpsM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Js6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.generate.javadoc" commandName="Generate JSDoc" description="Generates JSDoc for a selectable set of JavaScript resources" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7J86yEeaBOvrLLHWY2A" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_QrPQ186yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7KM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_QrOpwM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Kc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Ks6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7K86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7LM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Lc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Ls6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7L86yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.xmlFileUpgradeToLatestVersion" commandName="Upgrade JPA Document Version" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7MM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Mc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Ms6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7M86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7NM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Nc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.ProjectsView" commandName="JavaScript Projects" description="Show the Projects view" category="_QrOpts6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Ns6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7N86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7OM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Oc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_QrOpss6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrS7Os6yEeaBOvrLLHWY2A" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_QrS7O86yEeaBOvrLLHWY2A" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_QrS7PM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Pc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.javadoc.comment" commandName="Add JSDoc Comment" description="Add a JSDoc comment stub to the member element" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Ps6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_QrOpss6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrS7P86yEeaBOvrLLHWY2A" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_QrS7QM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Qc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy SHA-1" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Qs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7Q86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrS7RM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_QrOpuc6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrS7Rc6yEeaBOvrLLHWY2A" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_QrTiMM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiMc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_QrPQ0c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiMs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiM86yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Project" description="Update Maven Project configuration and dependencies" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiNM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.newMappingFile" commandName="JPA ORM Mapping File" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiNc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiNs6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiN86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiOM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiOc6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.debugAction" commandName="Debug" category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiOs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiO86yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiPM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.newEntity" commandName="JPA Entity" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiPc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_QrPQ1s6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrTiPs6yEeaBOvrLLHWY2A" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_QrTiP86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiQM6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_QrPQzc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiQc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_QrPQ0c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiQs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiQ86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiRM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiRc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiRs6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiR86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiSM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiSc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiSs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the JavaScript file" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiS86yEeaBOvrLLHWY2A" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiTM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_QrPQ1s6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrTiTc6yEeaBOvrLLHWY2A" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_QrTiTs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiT86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiUM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiUc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newEclipseLinkMappingFile" commandName="EclipseLink ORM Mapping File" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiUs6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commit.CherryPick" commandName="Cherry Pick" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiU86yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiVM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiVc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiVs6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiV86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrTiWM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJQM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsd.ui.refactor.makeElementGlobal" commandName="Make Local Element &amp;Global" description="Promotes local element to global level and replaces its references" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJQc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJQs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJQ86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJRM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJRc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJRs6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJR86yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJSM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_QrOpt86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJSc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJSs6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJS86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJTM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJTc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJTs6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJT86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor " description="Toggles linking of a view's selection with the active editor's selection" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJUM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJUc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJUs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJU86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJVM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJVc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJVs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJV86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJWM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJWc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJWs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJW86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Directory" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJXM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJXc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_QrPQ1c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJXs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJX86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJYM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJYc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJYs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.jsp.ui.add.imports" commandName="Add Im&amp;port" description="Create import declaration for selection" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJY86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJZM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newDynamicEntity" commandName="EclipseLink Dynamic Entity" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJZc6yEeaBOvrLLHWY2A" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_QrPQz86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJZs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUJZ86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwUM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwUc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwUs6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_QrPQ0s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwU86yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Terminal" category="_QrPQzM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwVM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwVc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_QrPQx86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwVs6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteCurrentAction" commandName="Execute Current Text" category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwV86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwWM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwWc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwWs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Field" description="Create getting and setting methods for the field and use only those to access the field" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwW86yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwXM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwXc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwXs6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwX86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.TypeHierarchy" commandName="JavaScript Type Hierarchy" description="Show the Type Hierarchy view" category="_QrOpts6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwYM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwYc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwYs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.convertJavaGenerators" commandName="Move Java Generators to XML..." category="_QrOpw86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwY86yEeaBOvrLLHWY2A" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_QrPQ1M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwZM6yEeaBOvrLLHWY2A" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_QrPQz86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwZc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwZs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwZ86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwaM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="%RebaseInteractiveCurrentHandler.name" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwac6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.debug" commandName="Debug XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwas6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_QrPQ0s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwa86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwbM6yEeaBOvrLLHWY2A" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_QrPQ1M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwbc6yEeaBOvrLLHWY2A" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_QrPQzs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwbs6yEeaBOvrLLHWY2A" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwb86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewRenameBranch" commandName="Rename Branch..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwcM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwcc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwcs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.pagedesigner.horizotal" commandName="Horizontal Layout" category="_QrPQ086yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwc86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwdM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwdc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwds6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwd86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUweM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwec6yEeaBOvrLLHWY2A" elementId="refresh.schema.editor.action.id" commandName="Refresh from Server" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwes6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_QrPQ1c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwe86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwfM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.MembersView" commandName="JavaScript Members" description="Show the Members view" category="_QrOpts6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwfc6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.result.removeInstance" commandName="Remove Result" category="_QrOpws6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwfs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwf86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.move.inner.to.top.level" commandName="Convert Member Type to Top Level" description="Convert member type to top level" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwgM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to var" description="Invokes quick assist and selects 'Assign to var'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrUwgc6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_QrPQzc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXYM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXYc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXYs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXY86yEeaBOvrLLHWY2A" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_QrPQ1M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXZM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXZc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXZs6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXZ86yEeaBOvrLLHWY2A" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_QrPQ1M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXaM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXac6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXas6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXa86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXbM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Last Edit Location" description="Last edit location" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXbc6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXbs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXb86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXcM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id To Clipboard" description="Copies the build id to the clipboard." category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXcc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXcs6yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.rcp.commands.openBrowser" commandName="Open a Web browser" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrVXc86yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_QrVXdM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXdc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXds6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXd86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXeM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXec6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.commands.showElementInTypeHierarchyView" commandName="Show JavaScript Element Type Hierarchy" description="Show a JavaScript element in the Type Hierarchy view" category="_QrPQ1s6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrVXes6yEeaBOvrLLHWY2A" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_QrVXe86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXfM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXfc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXfs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXf86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXgM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXgc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXgs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXg86yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.jsp.ui.refactor.move" commandName="Move" description="Move a Java Element to another package" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXhM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.java.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_QrPQws6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXhc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXhs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXh86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXiM6yEeaBOvrLLHWY2A" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXic6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXis6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXi86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXjM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXjc6yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.rcp.commands.extensionDiscovery" commandName="Discover New Extensions" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrVXjs6yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_QrVXj86yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXkM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXmlAndMap" commandName="Add Attribute to XML and Map..." category="_QrOpv86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXkc6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrVXks6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-cM6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.toggleCommentAction" commandName="Toggle Comment" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-cc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-cs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-c86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commit.CreateBranch" commandName="Create Branch..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-dM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-dc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-ds6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-d86yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API &amp;Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-eM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-ec6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-es6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-e86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-fM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commit.ShowInHistory" commandName="Show in History" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-fc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-fs6yEeaBOvrLLHWY2A" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-f86yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.core.synchronizeClasses" commandName="Synchronize Class List" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-gM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-gc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-gs6yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.rcp.commands.openBrowserDialog" commandName="Open a Web browser" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrV-g86yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_QrV-hM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-hc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-hs6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-h86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-iM6yEeaBOvrLLHWY2A" elementId="org.eclipse.emf.codegen.ecore.ui.Generate" commandName="Generate Code" description="Generate code for the EMF models in the workspace" category="_QrOpvs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-ic6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.TypesView" commandName="JavaScript Types" description="Show the Types view" category="_QrOpts6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-is6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-i86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-jM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-jc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in JavaScript editors" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-js6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-j86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-kM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.CherryPick" commandName="Cherry Pick" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-kc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.method" commandName="Extract Function" description="Extract a set of statements or an expression into a new function and use the new function" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-ks6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.ConfigureUpstreamFetch" commandName="Configure Upstream Fetch" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-k86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-lM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-lc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-ls6yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.rcp.commands.openPreferences" commandName="Open the preferences dialog" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrV-l86yEeaBOvrLLHWY2A" elementId="org.eclipse.recommenders.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_QrV-mM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-mc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new function parameter based on the selected expression" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-ms6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrV-m86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_QrV-nM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-nc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-ns6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_QrPQzc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-n86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-oM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools &amp;Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-oc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-os6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-o86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-pM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-pc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-ps6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_QrPQ0c6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrV-p86yEeaBOvrLLHWY2A" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_QrV-qM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-qc6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.saveAsTemplateAction" commandName="Save as Template" category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-qs6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrV-q86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlgM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.makePersistent" commandName="Make Persistent..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlgc6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_QrPQ0s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlgs6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with each other" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlg86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlhM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlhc6yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven Dependency" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlhs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlh86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWliM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlic6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrWlis6yEeaBOvrLLHWY2A" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_QrWli86yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWljM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWljc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.wsdl.ui.refactor.rename.element" commandName="Rename WSDL component" description="Renames WSDL component" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWljs6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlj86yEeaBOvrLLHWY2A" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlkM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlkc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.generate.xml" commandName="&amp;XML File..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlks6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlk86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWllM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWllc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlls6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWll86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlmM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commit.StashApply" commandName="Apply Stashed Changes" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlmc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlms6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlm86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlnM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlnc6yEeaBOvrLLHWY2A" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_QrPQ1M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlns6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWln86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWloM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_QrPQ1s6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrWloc6yEeaBOvrLLHWY2A" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_QrWlos6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlo86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlpM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlpc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlps6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlp86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_QrPQw86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlqM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlqc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlqs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlq86yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.pagedesigner.vertical" commandName="Vertical Layout" category="_QrPQ086yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlrM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlrc6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlrs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlr86yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.addToPersistenceUnit" commandName="Add to Persistence Unit" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlsM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlsc6yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlss6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWls86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWltM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWltc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlts6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWlt86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a JavaScript editor" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWluM6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSelectionAction" commandName="Execute Selected Text" category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrWluc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMkM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static var and uses the new static var" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMkc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMks6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.convertJavaProjectToJpa" commandName="Convert to JPA Project..." category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMk86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_QrPQ1c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMlM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.entityMappingsAddPersistentClass" commandName="Add Class..." category="_QrOpv86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMlc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMls6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMl86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_QrOpts6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrXMmM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_QrXMmc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_QrXMms6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_QrXMm86yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.copycolumn" commandName="Copy" category="_QrOpsc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMnM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMnc6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteAsOneStatementAction" commandName="Execute Selected Text As One Statement" category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMns6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMn86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMoM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to" description="Go to a particular resource in the active view" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMoc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMos6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMo86yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_QrPQ0s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMpM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMpc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMps6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMp86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMqM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMqc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMqs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMq86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_QrOpwM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMrM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_QrPQxM6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrXMrc6yEeaBOvrLLHWY2A" elementId="title" name="Title"/>
    <parameters xmi:id="_QrXMrs6yEeaBOvrLLHWY2A" elementId="message" name="Message"/>
    <parameters xmi:id="_QrXMr86yEeaBOvrLLHWY2A" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_QrXMsM6yEeaBOvrLLHWY2A" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_QrXMsc6yEeaBOvrLLHWY2A" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_QrXMss6yEeaBOvrLLHWY2A" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_QrXMs86yEeaBOvrLLHWY2A" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_QrXMtM6yEeaBOvrLLHWY2A" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_QrXMtc6yEeaBOvrLLHWY2A" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_QrXMts6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMt86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMuM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMuc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to var" description="Invokes quick assist and selects 'Convert local variable to var'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMus6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMu86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMvM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMvc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMvs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsd.ui.refactor.makeTypeGlobal" commandName="Make &amp;Anonymous Type Global" description="Promotes anonymous type to global level and replaces its references" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMv86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMwM6yEeaBOvrLLHWY2A" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMwc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Functions" description="Add delegate functions for a type's vars" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMws6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXMw86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzoM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzoc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzos6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzo86yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.connectivity.commands.export" commandName="Export Profiles Command" description="Command to export connection profiles" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzpM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewRefresh" commandName="Refresh" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzpc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_QrPQ0c6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrXzps6yEeaBOvrLLHWY2A" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_QrXzp86yEeaBOvrLLHWY2A" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_QrXzqM6yEeaBOvrLLHWY2A" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_QrXzqc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.lockToolBar" commandName="Lock the Toolbars" description="Lock the Toolbars" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzqs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzq86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzrM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_QrOpuc6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrXzrc6yEeaBOvrLLHWY2A" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_QrXzrs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_QrPQ0c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzr86yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import Plug-in from a Repository" description="Imports a plug-in from a source repository" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzsM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzsc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzss6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_QrPQ0s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzs86yEeaBOvrLLHWY2A" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_QrPQ0c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXztM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXztc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.eclipselink.ui.convertJavaConverters" commandName="Move Java Converters to XML..." category="_QrOpw86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzts6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.RenameBranch" commandName="Rename Branch..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzt86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter functions for type's vars" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzuM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzuc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzus6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzu86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzvM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzvc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzvs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzv86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzwM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzwc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commit.StashDrop" commandName="Delete Stashed Commit..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzws6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzw86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Tag" commandName="Tag" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzxM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzxc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzxs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzx86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzyM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzyc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_QrOpuc6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrXzys6yEeaBOvrLLHWY2A" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_QrXzy86yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_QrPQzc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzzM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_QrPQ186yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzzc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXzzs6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrXzz86yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_QrXz0M6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.ConfigureUpstreamPush" commandName="Configure Upstream Push" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz0c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz0s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz086yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz1M6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz1c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz1s6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz186yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz2M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_QrPQyc6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrXz2c6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_QrXz2s6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz286yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz3M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz3c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz3s6yEeaBOvrLLHWY2A" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz386yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrXz4M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_QrOpsM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYasM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYasc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYass6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYas86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYatM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYatc6yEeaBOvrLLHWY2A" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYats6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYat86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYauM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYauc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYaus6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYau86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYavM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYavc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYavs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYav86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_QrPQ1c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYawM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYawc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYaws6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYaw86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYaxM6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYaxc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected function" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYaxs6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Link with Selection" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYax86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYayM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYayc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYays6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Annotations" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYay86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYazM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYazc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYazs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.debug.ui.breakpoint.properties" commandName="JavaScript Breakpoint Properties" description="View and edit the properties for a given JavaScript breakpoint" category="_QrPQy86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYaz86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa0M6yEeaBOvrLLHWY2A" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_QrOpts6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa0c6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Referehs Remote Cache" category="_QrPQzc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa0s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa086yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.CreateBranch" commandName="Create Branch" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa1M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa1c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa1s6yEeaBOvrLLHWY2A" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa186yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa2M6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa2c6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa2s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_QrPQ1s6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrYa286yEeaBOvrLLHWY2A" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_QrYa3M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.debug.ui.evaluate.command" commandName="Evaluate" description="Evaluates the selected text in the JavaScript editor" category="_QrPQy86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa3c6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_QrPQzM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa3s6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa386yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa4M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa4c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa4s6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSQLAction" commandName="Execute All" category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa486yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa5M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa5c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa5s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa586yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa6M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa6c6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Checkout" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa6s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa686yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa7M6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.commands.openscrapbook" commandName="Open SQL Scrapboo&amp;k" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa7c6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.ws.jaxws.ui.configure.handlers" commandName="Configure Handlers" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa7s6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa786yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method in its hierarchy" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa8c6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.JavaBrowsingPerspective" commandName="JavaScript Browsing" description="Show the JavaScript Browsing perspective" category="_QrOpsM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa8s6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa886yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa9M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa9c6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrYa9s6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.show.in.package.view" commandName="Show in Script Explorer" description="Show the selected element in the Script Explorer" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBwM6yEeaBOvrLLHWY2A" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_QrPQ1M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBwc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBws6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBw86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBxM6yEeaBOvrLLHWY2A" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_QrPQ1M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBxc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBxs6yEeaBOvrLLHWY2A" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_QrPQ0c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBx86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZByM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected function" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZByc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBys6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.Revert" commandName="Revert Commit" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBy86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBzM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZBzc6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.connectivity.commands.addprofile" commandName="New Connection Profile Command" description="Command to create a new connection profile" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrZBzs6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.connectivity.ui.ignoreCategory" name="ignoreCategory"/>
    <parameters xmi:id="_QrZBz86yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.connectivity.ui.useSelection" name="useSelection"/>
  </commands>
  <commands xmi:id="_QrZB0M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB0c6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB0s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_QrOpsM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB086yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or function" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB1M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB1c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB1s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB186yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.connectivity.commands.addrepository" commandName="New Repository Profile Command" description="Command to create a new repository profile" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB2M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB2c6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB2s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB286yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.run" commandName="Run XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB3M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB3c6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.generateEntities" commandName="Generate Entities from Tables..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB3s6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.result.removeAllInstances" commandName="Remove All Visible Results" category="_QrOpws6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB386yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB4M6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB4c6yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB4s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB486yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into JavaScript comments" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB5M6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB5c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB5s6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB586yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB6M6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.pastecolumn" commandName="Paste" category="_QrOpsc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB6c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB6s6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_QrOpvM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB686yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB7M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB7c6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Var" description="Create getting and setting functions for the var and use only those to access the var" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB7s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB786yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_QrOpsM6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrZB8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_QrZB8c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_QrZB8s6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB886yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB9M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB9c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB9s6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB986yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB-M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB-c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB-s6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_QrPQw86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB-86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB_M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB_c6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB_s6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZB_86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZCAM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo0M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo0c6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrZo0s6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_QrZo086yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo1M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo1c6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_QrPQ1s6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrZo1s6yEeaBOvrLLHWY2A" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_QrZo186yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.PackageExplorer" commandName="JavaScript Script Explorer" description="Show the Script Explorer" category="_QrOpts6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo2M6yEeaBOvrLLHWY2A" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_QrPQzs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo2c6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.jsp.ui.refactor.rename" commandName="Rename" description="Rename a Java Element" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo2s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_QrPQ0c6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrZo286yEeaBOvrLLHWY2A" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_QrZo3M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo3c6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo3s6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_QrPQ1c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo386yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo4M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo4c6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo4s6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo486yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo5M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo5c6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.callhierarchy.view" commandName="JavaScript Call Hierarchy" description="Show the Call Hierarchy view" category="_QrOpts6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo5s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo586yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.JavadocView" commandName="Documentation" description="Show the JavaScript Documentation view" category="_QrOpts6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo6M6yEeaBOvrLLHWY2A" elementId="org.eclipse.rse.shells.ui.actions.LaunchShellCommand" commandName="Launch Shell" category="_QrOpus6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo6c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo6s6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsd.ui.refactor.rename.element" commandName="&amp;Rename XSD element" description="Rename XSD element" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo686yEeaBOvrLLHWY2A" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_QrPQ0c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo7M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo7c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo7s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo786yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo8c6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo8s6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo886yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo9M6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo9c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo9s6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.PackagesView" commandName="JavaScript Folders" description="Show the Folders view" category="_QrOpts6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo986yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo-M6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo-c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo-s6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo-86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo_M6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo_c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo_s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZo_86yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZpAM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZpAc6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.connectivity.commands.showcategory" commandName="Show Category" description="Show Category" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZpAs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.pagedesigner.design" commandName="Graphical Designer" category="_QrPQ086yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZpA86yEeaBOvrLLHWY2A" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZpBM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZpBc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZpBs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZpB86yEeaBOvrLLHWY2A" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrZpCM6yEeaBOvrLLHWY2A" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_QrZpCc6yEeaBOvrLLHWY2A" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_QrZpCs6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZpC86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrZpDM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_QrOpss6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrZpDc6yEeaBOvrLLHWY2A" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_QraP4M6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP4c6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QraP4s6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_QraP486yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Search" description="Adds all plug-ins in the target platform to java search" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP5M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP5c6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_QrPQzc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP5s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP586yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_QrPQ1c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP6M6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP6c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP6s6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.runAction" commandName="Run" category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP686yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP7M6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP7c6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove &amp;API Problem Filters..." description="Remove API problem filters for this project" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP7s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP786yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP8M6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP8c6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.views.XPathView.processor.xpathprocessor" commandName="XPath Processor" category="_QrOptM6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QraP8s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_QraP886yEeaBOvrLLHWY2A" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_QrPQ186yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP9M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP9c6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.commands.showElementInPackageView" commandName="Show JavaScript Element in Script Explorer" description="Select JavaScript element in the Script Explorer view" category="_QrPQ1s6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QraP9s6yEeaBOvrLLHWY2A" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_QraP986yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP-M6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP-c6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP-s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP-86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP_M6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP_c6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP_s6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraP_86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.CheckoutCommand" commandName="Checkout" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQAM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQAc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQAs6yEeaBOvrLLHWY2A" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQA86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQBM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.commands.openElementInEditor" commandName="Open JavaScript Element" description="Open a JavaScript element in its editor" category="_QrPQ1s6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QraQBc6yEeaBOvrLLHWY2A" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_QraQBs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() functions for the type" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQB86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQCM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQCc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQCs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQC86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQDM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQDc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQDs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_QrPQxM6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QraQD86yEeaBOvrLLHWY2A" elementId="title" name="Title"/>
    <parameters xmi:id="_QraQEM6yEeaBOvrLLHWY2A" elementId="message" name="Message"/>
    <parameters xmi:id="_QraQEc6yEeaBOvrLLHWY2A" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_QraQEs6yEeaBOvrLLHWY2A" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_QraQE86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QraQFM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_QraQFc6yEeaBOvrLLHWY2A" elementId="revert.schema.editor.action.id" commandName="Revert Object" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQFs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_QrOpwM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQF86yEeaBOvrLLHWY2A" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQGM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQGc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQGs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQG86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.navigate.gotopackage" commandName="Go to Folder" description="Go to Folder" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQHM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQHc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQHs6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commit.Revert" commandName="Revert Commit" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQH86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_QrPQw86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQIM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Repository" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QraQIc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory function to encapsulate invocation of the selected constructor" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra28M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra28c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra28s6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra2886yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra29M6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commit.Checkout" commandName="Checkout" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra29c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra29s6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra2986yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra2-M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra2-c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra2-s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra2-86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra2_M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra2_c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Dynamic Help" description="Open the dynamic help" category="_QrPQ0c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra2_s6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_QrPQ2c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra2_86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_QrPQw86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3AM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Ac6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.saveToDatabaseAction" commandName="Save to Database" category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3As6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Var" description="Convert a local variable to a var" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3A86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.views.XPathView.prefixes" commandName="&amp;Edit Namespace Prefixes" category="_QrOptM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3BM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jaxb.eclipselink.ui.command.addEclipseLinkJaxbProperty" commandName="Add EclipseLink JAXB property" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Bc6yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Bs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3B86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3CM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Cc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Cs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3C86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3DM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Dc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Ds6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.result.terminate" commandName="Terminate Result" category="_QrOpws6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3D86yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jaxb.ui.command.createPackageInfo" commandName="Create package-info.java" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3EM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Ec6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Es6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_QrOptc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3E86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3FM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Fc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Fs6yEeaBOvrLLHWY2A" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3F86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3GM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Gc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Gs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ide.configureFilters" commandName="Configure Contents..." description="Configure the filters to apply to the markers view" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3G86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected JavaScript comment lines" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3HM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Hc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Hs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xsd.ui.refactor.renameTargetNamespace" commandName="Rename Target Namespace" description="Changes the target namespace of the schema" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3H86yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3IM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Ic6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Is6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3I86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3JM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Jc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the JavaScript file" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Js6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3J86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_QrOpss6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_Qra3KM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_Qra3Kc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Ks6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3K86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.modify.method.parameters" commandName="Change Function Signature" description="Change function signature includes parameter names and parameter order" category="_QrOpxM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3LM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Lc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Ls6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3L86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_QrPQyM6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_Qra3MM6yEeaBOvrLLHWY2A" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_Qra3Mc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Ms6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3M86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3NM6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_QrPQzc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Nc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to var" description="Invokes quick assist and selects 'Assign parameter to var'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Ns6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3N86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3OM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_QrOpss6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_Qra3Oc6yEeaBOvrLLHWY2A" elementId="url" name="URL"/>
    <parameters xmi:id="_Qra3Os6yEeaBOvrLLHWY2A" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_Qra3O86yEeaBOvrLLHWY2A" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_Qra3PM6yEeaBOvrLLHWY2A" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_Qra3Pc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3Ps6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Qra3P86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeAM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.convertJavaQueries" commandName="Move Java Queries to XML..." category="_QrOpw86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeAc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeAs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.pagedesigner.source" commandName="Source Code" category="_QrPQ086yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeA86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeBM6yEeaBOvrLLHWY2A" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_QrPQzs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeBc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeBs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeB86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeCM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeCc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_QrPQ0c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeCs6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_QrPQ1c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeC86yEeaBOvrLLHWY2A" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_QrPQ1M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeDM6yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeDc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.dbws.ui.generateDbws" commandName="Generate Database Web Services" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeDs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeD86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.refreshFromDatabaseAction" commandName="Refresh from Database" category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeEc6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeEs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeE86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeFM6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.GotoMatchingTokenAction" commandName="Goto Matching Token" category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeFc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeFs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeF86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.override.methods" commandName="Override/Implement Functions" description="Override or implement functions from super types" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeGM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Vars" description="Choose vars to initialize and constructor from superclass to call " category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeGc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeGs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeG86yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeHM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeHc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commit.CreateTag" commandName="Create Tag..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeHs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeH86yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeIM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeIc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.internal.reflog.CheckoutCommand" commandName="Checkout" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeIs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_QrPQxs6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeI86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_QrPQ0c6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeJM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeJc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeJs6yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeJ86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeKM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.SourceView" commandName="JavaScript Declaration" description="Show the Declaration view" category="_QrOpts6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeKc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeKs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify var access" description="Invokes quick assist and selects 'Qualify var access'" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeK86yEeaBOvrLLHWY2A" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeLM6yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven Plugin" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeLc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeLs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeL86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeMM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_QrOpuc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeMc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.eclipselink.ui.persistentTypeAddVirtualAttribute" commandName="Add Virtual Attribute..." category="_QrOpv86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeMs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeM86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeNM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeNc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeNs6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.quickAccess" commandName="Quick Access" description="Quickly access UI elements" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeN86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeOM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeOc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.persistentTypeMapAs" commandName="Map As" category="_QrOpv86yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrbeOs6yEeaBOvrLLHWY2A" elementId="persistentTypeMappingKey" name="mapping key" optional="false"/>
  </commands>
  <commands xmi:id="_QrbeO86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_QrPQyM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbePM6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression " category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbePc6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbePs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeP86yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeQM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeQc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeQs6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.DMLDialogSelectionAction" commandName="Edit in SQL Query Builder..." category="_QrOps86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeQ86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_QrPQ1s6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrbeRM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_QrbeRc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeRs6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_QrOpwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeR86yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.generateDDL" commandName="Generate Tables from Entities..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeSM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.discovery.ui.discoveryWizardCommand" commandName="Discovery Wizard" description="shows the connector discovery wizard" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeSc6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeSs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrbeS86yEeaBOvrLLHWY2A" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_QrPQys6yEeaBOvrLLHWY2A">
    <parameters xmi:id="_QrbeTM6yEeaBOvrLLHWY2A" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_QrcFEM6yEeaBOvrLLHWY2A" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrcFEc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_QrOpss6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrcFEs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_QrPQ2s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrcFE86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_QrPQyc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrcFFM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_QrPQxc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrcFFc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_QrPQ1s6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrcFFs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_QrPQ2M6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrcFF86yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrcFGM6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_QrPQwc6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrcFGc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_QrPQwM6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrcFGs6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_QrcFG86yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_QrOpu86yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9uu8M6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9uu8c6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet/org.eclipse.datatools.sqltools.sqlscrapbook.actions.OpenScrapbookAction" commandName="Open SQL Scrapbook" description="Open scrapbook to edit SQL statements" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9wkIM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9wkIc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9xLMM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9xLMc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9xLMs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9xLM86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9xLNM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9xLNc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9xLNs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q90OgM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q90Ogc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q90Ogs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q901kM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q91coM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEArtifact" commandName="Servlet" description="Create a new Servlet" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q91coc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEProject" commandName="Dynamic Web Project" description="Create a Dynamic Web project" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q91cos6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.java.actionSet.browsing/org.eclipse.mylyn.java.ui.actions.ApplyMylynToBrowsingPerspectiveAction" commandName="Focus Browsing Perspective" description="Focus Java Browsing Views on Active Task" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q91co86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.doc.actionSet/org.eclipse.mylyn.tasks.ui.bug.report" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q91cpM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q92DsM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q92Dsc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q92Dss6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.rse.core.search.searchActionSet/org.eclipse.rse.core.search.searchAction" commandName="Remote..." description="Opens Remote Search dialog page for text and file searching on remote systems" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q92Ds86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q92DtM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q92qwM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q92qwc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q92qws6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet/org.eclipse.wst.jsdt.ui.actions.OpenFileWizard" commandName="JavaScript Source File" description="New JavaScript file" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q93R0M6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet/org.eclipse.wst.jsdt.ui.actions.OpenProjectWizard" commandName="JavaScript Project..." description="New JavaScript Project" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q93R0c6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.refactor.show.refactoring.history" commandName="History..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q93R0s6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.SearchActionSet/org.eclipse.wst.jsdt.ui.actions.OpenJavaSearchPage" commandName="JavaScript..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9344M6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9344c6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9344s6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q934486yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newCSSFile" commandName="CSS" description="Create a new Cascading Style Sheet" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9345M6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newJSFile" commandName="JavaScript" description="Create a new JavaScript file" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9345c6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newHTMLFile" commandName="HTML" description="Create a new HTML page" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9345s6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.ws.explorer.explorer/org.eclipse.wst.ws.internal.explorer.action.LaunchWSEAction" commandName="Launch the Web Services Explorer" description="Launch the Web Services Explorer" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q94f8M6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q94f8c6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.rullerDoubleClick/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Add Breakpoint" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q94f8s6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.emf.exporter.genModelEditorContribution/org.eclipse.emf.exporter.ui.GenModelExportActionDelegate.Editor" commandName="Export Model..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q94f886yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.emf.importer.genModelEditorContribution/org.eclipse.emf.importer.ui.GenModelReloadActionDelegate.Editor" commandName="Reload..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q94f9M6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.RemoveMappingActionID" commandName="Remove Mapping" description="Remove the mapping associated with the selected objects." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q94f9c6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.TypeMatchMappingActionID" commandName="Match Mapping by Type" description="Create child mappings automatically by type." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q94f9s6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.NameMatchMappingActionID" commandName="Match Mapping by Name" description="Create child mappings automatically by name." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q94f986yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateOneSidedMappingActionID" commandName="Create One-sided Mapping" description="Create a new mapping for the selected object." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95HAM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateMappingActionID" commandName="Create Mapping" description="Create a new mapping between the selected objects." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95HAc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddOuputRootActionID" commandName="Add Output Root..." description="Add new output root." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95HAs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddInputRootActionID" commandName="Add Input Root..." description="Add new input root." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95HA86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95HBM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95HBc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95uEM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95uEc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95uEs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95uE86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95uFM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95uFc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q95uFs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q96VIM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q96VIc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q96VIs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q96VI86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q96VJM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q96VJc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q96VJs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q96VJ86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q968MM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q968Mc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q968Ms6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q968M86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q968NM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.jsdt.debug.ui.togglebreakpoint/org.eclipse.wst.jsdt.debug.ui.RulerToggleBreakpoint" commandName="Toggle Breakpoint" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q968Nc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q968Ns6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q97jQM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q97jQc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q97jQs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q97jQ86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.editor/org.eclipse.wst.wsdl.ui.actions.ReloadDependenciesActionDelegate" commandName="Reload Dependencies" description="Reload Dependencies" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q97jRM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q97jRc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q97jRs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q97jR86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q97jSM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98KUM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98KUc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98KUs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98KU86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98KVM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98xYM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98xYc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98xYs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98xY86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98xZM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98xZc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98xZs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q98xZ86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99YcM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99Ycc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99Ycs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99Yc86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99YdM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99Ydc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99Yds6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99_gM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99_gc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99_gs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99_g86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99_hM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99_hc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99_hs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q99_h86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9-mkM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9-mkc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9-mks6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9-mk86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9-mlM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9-mlc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9-mls6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_NoM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_Noc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_Nos6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_No86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_NpM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_Npc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_Nps6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_0sM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.java.ui.markers.breakpoints.contribution/org.eclipse.mylyn.java.ui.actions.focus.markers.breakpoints" commandName="Focus on Active Task" description="Focus on Active Task" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_0sc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.ui.debug.view.contribution/org.eclipse.mylyn.ui.actions.FilterResourceNavigatorAction" commandName="Focus on Active Task (Experimental)" description="Focus on Active Task (Experimental)" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_0ss6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_0s86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_0tM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_0tc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q9_0ts6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-AbwM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-Abwc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.java.explorer.contribution/org.eclipse.mylyn.java.actions.focus.packageExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-Abws6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-Abw86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-AbxM6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-Abxc6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-Abxs6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-Abx86yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-BC0M6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-BC0c6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.rse.ui.view.systemView.toolbar/org.eclipse.rse.ui.view.systemView.toolbar.linkWithSystemView" commandName="Link with Editor" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-BC0s6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.wst.jsdt.debug.ui.add.scriptload.breakpoint" commandName="Add Script Load Breakpoint" description="Add Script Load Breakpoint" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-BC086yEeaBOvrLLHWY2A" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Suspend For All Script Loads" description="Suspends when any script is loaded" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-BC1M6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.wst.jsdt.debug.ui.suspend.on.exceptions" commandName="Suspend On JavaScript Exceptions" description="Suspend on all JavaScript exceptions" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-BC1c6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::debugViewActions/org.eclipse.wst.jsdt.debug.ui.show.all.scripts" commandName="Show All Scripts" description="Shows or hides all scripts loaded in the visible targets" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-BC1s6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.functions" commandName="Show function variables" description="Show or hide function variables" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-Bp4M6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.this" commandName="Show 'this' variable" description="Show or hide the this variable" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-Bp4c6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.prototypes" commandName="Show proto variables" description="Show or hide proto variables" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <commands xmi:id="_Q-Bp4s6yEeaBOvrLLHWY2A" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.view/org.eclipse.wst.wsi.ui.internal.actions.actionDelegates.ValidateWSIProfileActionDelegate" commandName="WS-I Profile Validator" description="Validate WS-I Message Log File" category="_QrPQys6yEeaBOvrLLHWY2A"/>
  <addons xmi:id="_QqwIls6yEeaBOvrLLHWY2A" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_QqwIl86yEeaBOvrLLHWY2A" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_QqwImM6yEeaBOvrLLHWY2A" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_QqwImc6yEeaBOvrLLHWY2A" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_QqwIms6yEeaBOvrLLHWY2A" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_QqwIm86yEeaBOvrLLHWY2A" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_QqwInM6yEeaBOvrLLHWY2A" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_QqwInc6yEeaBOvrLLHWY2A" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_QqwIns6yEeaBOvrLLHWY2A" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_QqwIn86yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_Qq1BEM6yEeaBOvrLLHWY2A" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_QrOpsM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_QrOpsc6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaedtor.10x" name="ASA 9.x table schema editor"/>
  <categories xmi:id="_QrOpss6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_QrOps86yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.sqleditor.category" name="Database Tools" description="Database Development tools"/>
  <categories xmi:id="_QrOptM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.xml.views.XPathView" name="XPath"/>
  <categories xmi:id="_QrOptc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_QrOpts6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_QrOpt86yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
  <categories xmi:id="_QrOpuM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_QrOpuc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_QrOpus6yEeaBOvrLLHWY2A" elementId="org.eclipse.rse.ui.commands.category" name="Remote Systems"/>
  <categories xmi:id="_QrOpu86yEeaBOvrLLHWY2A" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_QrOpvM6yEeaBOvrLLHWY2A" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_QrOpvc6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_QrOpvs6yEeaBOvrLLHWY2A" elementId="org.eclipse.emf.codegen.ecore.ui.Commands" name="EMF Code Generation" description="Commands for the EMF code generation tools"/>
  <categories xmi:id="_QrOpv86yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.jpaStructureViewCommands" name="JPA Structure View"/>
  <categories xmi:id="_QrOpwM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_QrOpwc6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.category.source" name="Source" description="JavaScript Source Actions"/>
  <categories xmi:id="_QrOpws6yEeaBOvrLLHWY2A" elementId="org.eclipse.datatools.sqltools.result.category" name="SQL Results View"/>
  <categories xmi:id="_QrOpw86yEeaBOvrLLHWY2A" elementId="org.eclipse.jpt.jpa.ui.jpaMetadataConversionCommands" name="JPA Metadata Conversion"/>
  <categories xmi:id="_QrOpxM6yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.ui.category.refactoring" name="Refactor - JavaScript" description="JavaScript Refactoring Actions"/>
  <categories xmi:id="_QrPQwM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="%commands.category.name" description="%commands.category.description"/>
  <categories xmi:id="_QrPQwc6yEeaBOvrLLHWY2A" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_QrPQws6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.java.ui.commands" name="Java Context" description="Java Task-Focused Interface Commands"/>
  <categories xmi:id="_QrPQw86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_QrPQxM6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_QrPQxc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_QrPQxs6yEeaBOvrLLHWY2A" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_QrPQx86yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_QrPQyM6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_QrPQyc6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_QrPQys6yEeaBOvrLLHWY2A" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_QrPQy86yEeaBOvrLLHWY2A" elementId="org.eclipse.wst.jsdt.debug.ui.category" name="JavaScript Debug" description="Tooling for debugging JavaScript"/>
  <categories xmi:id="_QrPQzM6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_QrPQzc6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_QrPQzs6yEeaBOvrLLHWY2A" elementId="org.eclipse.team.ui.category.team" name="Team" description="Actions that apply when working with a Team"/>
  <categories xmi:id="_QrPQz86yEeaBOvrLLHWY2A" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_QrPQ0M6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_QrPQ0c6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_QrPQ0s6yEeaBOvrLLHWY2A" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_QrPQ086yEeaBOvrLLHWY2A" elementId="org.eclipse.jst.pagedesigner.pagelayout" name="Web Page Editor Layout"/>
  <categories xmi:id="_QrPQ1M6yEeaBOvrLLHWY2A" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_QrPQ1c6yEeaBOvrLLHWY2A" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_QrPQ1s6yEeaBOvrLLHWY2A" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_QrPQ186yEeaBOvrLLHWY2A" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_QrPQ2M6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_QrPQ2c6yEeaBOvrLLHWY2A" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_QrPQ2s6yEeaBOvrLLHWY2A" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
</application:Application>
