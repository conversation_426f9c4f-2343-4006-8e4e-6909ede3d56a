!SESSION 2016-12-30 11:05:32.400 -----------------------------------------------
eclipse.buildId=4.5.0.I20150603-2000
java.version=1.7.0_03
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86, WS=win32, NL=es_ES
Framework arguments:  -product org.eclipse.epp.package.jee.product
Command-line arguments:  -os win32 -ws win32 -arch x86 -product org.eclipse.epp.package.jee.product

!ENTRY org.eclipse.egit.ui 2 0 2016-12-30 11:06:16.324
!MESSAGE Warning: The environment variable HOME is not set. The following directory will be used to store the Git
user global configuration and to define the default location to store repositories: 'C:\Users\<USER>\Users\tienda.admin'. If this is
not correct please set the HOME environment variable and restart Eclipse. Otherwise Git for Windows and
EGit might behave differently since they see different configuration options.
This warning can be switched off on the Team > Git > Confirmations and Warnings preference page.

!ENTRY org.eclipse.core.jobs 2 2 2016-12-30 11:08:40.211
!MESSAGE Job found still running after platform shutdown.  Jobs should be canceled by the plugin that scheduled them during shutdown: org.eclipse.oomph.setup.ui.SetupUIPlugin$1$1
