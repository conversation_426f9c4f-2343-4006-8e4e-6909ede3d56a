/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.conexion;

import com.coppel.wsconciliacion.constantes.Constantes;
import com.coppel.wsconciliacion.enumerador.EnumConfiguracion;
import com.coppel.wsconciliacion.utilidades.Utilidades;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.logging.FileHandler;
import java.util.logging.Logger;
import java.util.logging.SimpleFormatter;
import java.util.Date;
import java.util.Properties;

/**
 * Clase para conectar con las bases de datos ingresos y cajas.
 * <AUTHOR>
 *
 */
public class ConexionConciliacion {
	public static final String driver = "org.postgresql.Driver";
	String connectString = "";
	String direccion = "";
	String baseDatos = "";
	String user = "";
	String pasConexConci = "";
	static String rutaLog = "";
	public static Logger log = Logger.getLogger("LogConciliacion");
	public static FileHandler fh = null;  
	public static String rutaEstatus = "";
	public static String rutaEstadoLog = "";
	public static String rutaCorreoLog = "";
	public static String rutaFechaLog = "";
	static int procesoE = 0;
	DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
	StringBuilder nombreLog = new StringBuilder();
	
	Properties propiedades;
	InputStream inputStream;
	/**
	 * Constructor de la clase ConexionConciliacion
	 * @throws IOException 
	 */
	public ConexionConciliacion() throws IOException
	{		
		String propFileName = "config.properties";
		try{
			if (Utilidades.checkAuthorization(Constantes.AUTHORIZATION)) {
				inputStream = getClass().getClassLoader().getResourceAsStream(propFileName);
				propiedades = new Properties();
				if (inputStream != null) {
					propiedades.load(inputStream);
					} else {
						throw new FileNotFoundException("2|" + "property file '" + propFileName + "' not found in the classpath");
					}
			}
			
			rutaEstatus = propiedades.getProperty("rutaEstatus");

			nombreLog = new StringBuilder();
			rutaLog = propiedades.getProperty("rutaConciliacionLog");
			nombreLog.append(rutaLog);
			nombreLog.append(dateFormat.format(new Date()));
			nombreLog.append(".log");
			rutaLog = nombreLog.toString();
					
			nombreLog = new StringBuilder();
			rutaEstadoLog = propiedades.getProperty("rutaConsultarEstadoLog");
			nombreLog.append(rutaEstadoLog);
			nombreLog.append(dateFormat.format(new Date()));
			nombreLog.append(".log");
			rutaEstadoLog = nombreLog.toString();
			
			nombreLog = new StringBuilder();
			rutaCorreoLog = propiedades.getProperty("rutaCorreoLog");
			nombreLog.append(rutaCorreoLog);
			nombreLog.append(dateFormat.format(new Date()));
			nombreLog.append(".log");
			rutaCorreoLog = nombreLog.toString();
			
			nombreLog = new StringBuilder();
			rutaFechaLog = propiedades.getProperty("rutaFechaLog");
			nombreLog.append(rutaFechaLog);
			nombreLog.append(dateFormat.format(new Date()));
			nombreLog.append(".log");
			rutaFechaLog = nombreLog.toString();
			nombreLog = new StringBuilder();

		} catch(Exception e){
			log.info(e.getMessage());
		}
		finally {
			inputStream.close();
		}				
	} 
	/**
	 * Metodo para leer los archivos de configuracion para conectar a la base
	 * de datos.
	 * @param conexion recibe un entero que indica a que base de datos debe de conectar.
	 * @return una conexion (Connection) a ingresos o a cajas.
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public  Connection LeerArchivoConexion(int conexion ) throws SQLException,
	ClassNotFoundException, SecurityException, NullPointerException, IOException{
		BufferedReader br = null;
		StringBuilder cadena = new StringBuilder();
		Connection con = null;
		try {
			
			if(conexion == EnumConfiguracion.BaseCajas.value)
			{
				escribirLog(procesoE,"---Conectar base de datos cajas---");
				direccion = propiedades.getProperty("servidorCajas");
				baseDatos = propiedades.getProperty("baseCajas");
				user = propiedades.getProperty("usuarioCajas");
				pasConexConci = propiedades.getProperty("contrasenaCajas");
			}
			if(conexion == EnumConfiguracion.BaseIngresos.value)
			{
				escribirLog(procesoE,"---Conectar base de datos ingresos---");
				direccion = propiedades.getProperty("servidorIngresos");
				baseDatos = propiedades.getProperty("baseIngresos");
				user = propiedades.getProperty("usuarioIngresos");
				pasConexConci = propiedades.getProperty("contrasenaIngresos");
			}
			Class.forName(driver);
			cadena.append("jdbc:postgresql://");
			cadena.append( direccion);
			cadena.append("/");
			cadena.append(baseDatos);
			cadena.append("?useUnicode=true&amp;amp;");
			cadena.append("characterEncoding=uft8");
			connectString =  cadena.toString();
			con = DriverManager.getConnection(connectString, user , pasConexConci);
			con.setAutoCommit(false);

		} finally {
			//try {
			//	if (br != null){
			//		br.close();
			//		con.close();
			//	}
			//} catch (IOException ex) {
			//	ex.getMessage();
			//	log.info(ex.getMessage());
			//	con.close();
			//}
			//con.close();
			//br.close();
		}
		return con;
	}
	
	/**
	 * Meto que incializa el log de escritura.
	 * @throws IOException 
	 * @throws SecurityException 
	 */
	public static void escribirLog(int proceso, String mensaje) throws SecurityException, 
	IOException, NullPointerException{
		procesoE = proceso;
		 System.setProperty("java.util.logging.SimpleFormatter.format", 
	        		"%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS %4$s ");
		if(proceso == 1){
			log = Logger.getLogger("LogConciliacion");
			fh = new FileHandler(rutaLog,true);
		}
		if(proceso == 2){
			log = Logger.getLogger("LogEstado");
			fh = new FileHandler(rutaEstadoLog,true);
		}
		if(proceso == 3){
			log = Logger.getLogger("LogCorreo");
			fh = new FileHandler(rutaCorreoLog,true);
		}
		if(proceso == 4){
			log = Logger.getLogger("LogFecha");
			//rutaFechaLog = "/sysx/progs/logs/conciliacion/logFechaConciliacion";
			fh = new FileHandler(rutaFechaLog,true);
		}
        log.addHandler(fh);
        SimpleFormatter formatter = new SimpleFormatter(); 
        fh.setFormatter(formatter);
        fh.setEncoding("UTF-8");
        log.setUseParentHandlers(false);
        log.info(mensaje);
        fh.flush();
        fh.close();
	}
	
	
}
