/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.enumerador;

/**
 * Enum para la configuracion de los valores duros.
 * <AUTHOR>
 *
 */
public enum EnumConfiguracion {
	CorreoDestino(1), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(2), MensajeCor<PERSON>o(3), SujetoCorreo(4), ServidorCorreo(5),
	FechaConciliacion(6), UsuarioCorreo(7), ContrasenaCorreo(8), CarpetaPagos(9),CarpetaCifras(10),
	BaseCajas(1), BaseIngresos(2), Direccion(1), Base(2), Usuario(3), Contrasena(4),
	tiendaRemanente(1), tiendaNormal(2), RutaConnect(11), UsuarioConnect(12),ContrasenaConnect(13),
	DominioDirect(14), MensajeCorreoNegativo(15), PuertoCorreo(16), AsuntoCorreoNegativo(17),
	RutaSftp(21),UsuarioSftp(22),ContrasenaSftp(23),PortSftp(24),HostSftp(25),PrivateKeySftp(26),
	UsuarioApiCorreos(27),ContrasenaApiCorreos(28),PathApiCorreos(29),PathApiTokenCorreos(30),ConectDirectSFTP(31);
	public final int value;

	/**
	 * Constructor del enum de configuracion.
	 * @param value valor que posee el enum de configuracion.
	 */
	private EnumConfiguracion(int value) {
		this.value = value;
	}
}
