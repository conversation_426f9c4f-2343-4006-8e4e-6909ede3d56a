/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.servicioweb;

import com.coppel.wsconciliacion.conexion.ConexionConciliacion;
import com.coppel.wsconciliacion.logica.ConciliacionDeDatos;

import java.io.IOException;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;

/**
 * Clase que contiene el metodo del webservice que ejecutara el proceso y sera
 * visible por el cliente.
 * 
 * <AUTHOR>
 *
 */
@WebService()
public class WsConciliacion {

	/**
	 * Metodo que sera visible para el cliente e iniciara el proceso de
	 * conciliacion.
	 * 
	 * @return un string que contiene el estado de la operacion.
	 * @throws java.io.IOException
	 * @throws Exception 
	 */
	@WebMethod(action = "generarConciliacion")
	public String generarConciliacion() {
		String estado = null;
		try{
			ConciliacionDeDatos cddConciliacion = new ConciliacionDeDatos(1);
			estado = cddConciliacion.generarConciliacion();
			ConexionConciliacion.escribirLog(1,estado);
		}
		catch(Exception e){
			try {
				ConexionConciliacion.escribirLog(4,e.getMessage());
				estado = "2|Ocurrio un error al generar la conciliacion.";
			} catch (SecurityException | NullPointerException | IOException e1) {
				// TODO Auto-generated catch block
				estado = "2|Ocurrio un error al crear el log.";
				e1.getMessage();
			}
			
		}
		return estado;
	}

	/**
	 * Metodo que sera invocado mediante un cliente para obtener la ultima
	 * fecha.
	 * 
	 * @return un string indicando el resultado de la operacion.
	 * @throws Exception 
	 */
	@WebMethod(action = "obtenerUltimaFecha")
	public String obtenerUltimaFecha(){
		String estado = null;
		try{
			ConciliacionDeDatos cddConciliacion = new ConciliacionDeDatos(4);
			estado = cddConciliacion.obtenerUltimaFecha();
		}
		catch(Exception e){
			try {
				ConexionConciliacion.escribirLog(4,e.getMessage());
				estado = "2|Ocurrio un error al obtener la ultima fecha. Error en el metodo obtenerUltimaFecha";
			} catch (SecurityException | NullPointerException | IOException e1) {
				// TODO Auto-generated catch block
				estado = "2|Ocurrio un error al crear el log.";
				e1.getMessage();
			}
			
		}
		return estado;
	}

	/**
	 * Metodo para reenviar el correo, en caso de error.
	 * @param peticion entero que indica si se reenviara (1) o se eliminaran los archivos.
	 * @return un string indicando
	 */
	@WebMethod(action = "reenviarCorreo")
	public String reenviarCorreo(@WebParam(name = "peticion") int peticion){
		String estado = null;
		try{
			ConciliacionDeDatos cddConciliacion = new ConciliacionDeDatos(3);
			if (peticion == 1) {
				estado = cddConciliacion.reenviarCorreo();
			} else {
				estado = cddConciliacion.eliminacionArchivos();
			}
		}
		catch(Exception e){
			try {
				ConexionConciliacion.escribirLog(3,e.getMessage());
				estado = "2|Ocurrio un error al reenviar el correo.";
			} catch (SecurityException | NullPointerException | IOException e1) {
				// TODO Auto-generated catch block
				estado = "2|Ocurrio un error al crear el log.";
				e1.getMessage();
			}
		}
		return estado;
	}
	
	/**
	 * Metodo para consultar el estado de el proceso de conciliacion.
	 * @return un string indicando
	 */
	@WebMethod(action = "consultarEstadoConciliacion")
	public String consultarEstadoConciliacion(){
		String estado = null;
		try{
			ConciliacionDeDatos cddConciliacion = new ConciliacionDeDatos(2);
			estado = cddConciliacion.consultarEstado();
		}
		catch(Exception e){
			try {
				ConexionConciliacion.escribirLog(2,e.getMessage());
				estado = "2|Ocurrio un error al consultar el estado de la conciliacion.";
			} catch (SecurityException | NullPointerException | IOException e1) {
				// TODO Auto-generated catch block
				estado = "2|Ocurrio un error al crear el log.";
				e1.getMessage();
			}
		}
		return estado;
	}
	
	/**
	 * Metodo para reenviar el archivo al connect direct, en caso de error.
	 * @param peticion entero que indica si se reenviara (1) o se eliminaran los archivos.
	 * @return un string indicando
	 */
	@WebMethod(action = "reenviarArchivoConnect")
	public String reenviarArchivoConnect(@WebParam(name = "peticion") int peticion){
		String estado = null;
		try{
			ConciliacionDeDatos cddConciliacion = new ConciliacionDeDatos(1);
			if (peticion == 1) {
				estado = cddConciliacion.reenviarArchivoConnect();
			} else {
				estado = cddConciliacion.eliminacionArchivos();
			}
		}
		catch(Exception e){
			try {
				ConexionConciliacion.escribirLog(3,e.getMessage());
				estado = "2|Ocurrio un error al depositar el archivo en el repositorio connect direct.";
			
			} catch (SecurityException | NullPointerException | IOException e1) {
				// TODO Auto-generated catch block
				estado = "2|Ocurrio un error al crear el log.";
				e1.getMessage();
			}
		}
		return estado;
	}

}
