/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.entidades;

//import com.sun.org.apache.bcel.internal.util.Objects;
import java.util.Objects;

import java.text.DateFormat;
import java.text.SimpleDateFormat;

import java.util.Date;

/**
 * Clase que sirve como modelo de datos para las tiendas que se encuentran en el log
 * ya sean remanentes o pendientes.
 * <AUTHOR>
 *
 */
public class LogTiendas {
	private String logTiendaConciliacion;
	private int numTienda;
	private int montoTotal;
	private int totalProcesado;
	private Date fechaConciliacion;
	private String usuarioCreacion;
	private String usuarioModificacion;
	private Date fechaPendiente;
	
	/**
	 * Constructor de la clase LogTiendas.
	 */
	public LogTiendas() {
		logTiendaConciliacion = "";
		numTienda = 0;
		montoTotal = 0;
		totalProcesado = 0;
		fechaConciliacion = new Date();
		usuarioCreacion = "";
		usuarioModificacion = "";
		fechaPendiente = new Date();
	}

	public String getLogTiendaConciliacion() {
		return logTiendaConciliacion;
	}

	public void setLogTiendaConciliacion(String logTiendaConciliacion) {
		this.logTiendaConciliacion = logTiendaConciliacion;
	}

	public int getNumTienda() {
		return numTienda;
	}

	public void setNumTienda(int numTienda) {
		this.numTienda = numTienda;
	}

	public int getMontoTotal() {
		return montoTotal;
	}

	public void setMontoTotal(int montoTotal) {
		this.montoTotal = montoTotal;
	}

	public String getUsuarioCreacion() {
		return usuarioCreacion;
	}

	public void setUsuarioCreacion(String usuarioCreacion) {
		this.usuarioCreacion = usuarioCreacion;
	}

	public String getUsuarioModificacion() {
		return usuarioModificacion;
	}

	public void setUsuarioModificacion(String usuarioModificacion) {
		this.usuarioModificacion = usuarioModificacion;
	}

        public String getFechaConciliacionString() {
    	DateFormat formato = new SimpleDateFormat("yyyy-MM-dd");
		String fecha = formato.format(getFechaConciliacion());
		return fecha;
	}
        
	public Date getFechaConciliacion() {
		return fechaConciliacion;
	}

	public void setFechaConciliacion(Date fechaConciliacion) {
		this.fechaConciliacion = fechaConciliacion;
	}

	public int getTotalProcesado() {
		return totalProcesado;
	}

	public void setTotalProcesado(int totalProcesado) {
		this.totalProcesado = totalProcesado;
	}
	
    
	public Date getFechaPendiente() {
		return fechaPendiente;
	}

	public void setFechaPendiente(Date fechaPendiente) {
		this.fechaPendiente = fechaPendiente;
	}
	
	@Override    
	public boolean equals(Object logTienda) {
		DateFormat formato = new SimpleDateFormat("yyyy-MM-dd");
		String fechaComparar = formato.format(getFechaConciliacion());
		String fechaComparar2 = formato.format(((LogTiendas) logTienda).getFechaConciliacion());
		if(((LogTiendas) logTienda).getNumTienda() == 0){
			return (logTienda instanceof LogTiendas) 
			         ? Objects.equals(fechaComparar , fechaComparar2 )
			         : (logTienda == this);
		}
		else{
			return (logTienda instanceof LogTiendas) 
			         ? getNumTienda() == (((LogTiendas) logTienda).getNumTienda())
			         : (logTienda == this);
		}
	}

}
