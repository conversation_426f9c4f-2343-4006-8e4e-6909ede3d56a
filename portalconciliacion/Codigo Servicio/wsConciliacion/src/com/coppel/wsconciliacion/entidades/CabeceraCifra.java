/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.entidades;

import java.util.Date;

/**
 * Clase que sirve como el modelo de datos para la cabecera de la cifra de control 
 * los movimientos normales.
 * <AUTHOR>
 *
 */
public class CabeceraCifra {
	private Date diaConciliado;
	private int tiendasProcesadas;
	private float porcentaje;
	private int montoDelDia;
	private int numTotalTiendas;
	/**
	 * Constructor de la clase CabeceraCifra, y se inicializan los valores.
	 */
	public CabeceraCifra(){
		tiendasProcesadas = 0;
		porcentaje = 0;
		montoDelDia = 0;
		diaConciliado = new Date();
	}
	
	public Date getDiaConciliado() {
		return diaConciliado;
	}
	public void setDiaConciliado(Date diaConciliado) {
		this.diaConciliado = diaConciliado;
	}
	public int getTiendasProcesadas() {
		return tiendasProcesadas;
	}
	public void setTiendasProcesadas(int tiendasProcesadas) {
		this.tiendasProcesadas = tiendasProcesadas;
	}
        public float obtenerPorcentaje() {
                
		int totalTiendas = this.getNumTotalTiendas();
                int totPor100 = this.getTiendasProcesadas()*100;
                float tot = (float)((float)totPor100/(float)totalTiendas);
                if (tot>0 && tot<1)
                {
                    tot = 1;
                }else{
                    if(tot>99 &&tot<100){
                        tot = 99;
                    }else{
                        tot = Math.round(tot);
                    }
                }
                return tot;
	}
	public float getPorcentaje() {
		return porcentaje;
	}
	public void setPortcentaje(float portcentaje) {
		this.porcentaje = portcentaje;
	}
	public int getMontoDelDia() {
		return montoDelDia;
	}
	public void setMontoDelDia(int montoDelDia) {
		this.montoDelDia = montoDelDia;
	}

        public int getNumTotalTiendas() {
            return numTotalTiendas;
        }

        public void setNumTotalTiendas(int numTotalTiendas) {
            this.numTotalTiendas = numTotalTiendas;
        }
}
