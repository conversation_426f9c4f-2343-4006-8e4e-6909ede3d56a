/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.entidades;

import java.util.ArrayList;
import java.util.List;

/**
 * Clase que se usa como modelo de datos para la cifra de control.
 * <AUTHOR>
 *
 */
public class CifraControl {
	private List<CabeceraCifra> cabecera;
	private List<LogTiendas> fechaRemanente;
	private List<LogTiendas> tiendaRemanente;
	private List<LogTiendas> tiendaPendiente;
	private List<LogTiendas> fechaPendiente;
	
	/**
	 * Constructor de la clase CifraControl, inicializa las variables.
	 */
	public CifraControl(){
		cabecera = new ArrayList<CabeceraCifra>();
		fechaRemanente = new ArrayList<LogTiendas>();
		tiendaRemanente = new ArrayList<LogTiendas>();
		tiendaPendiente = new ArrayList<LogTiendas>();
		fechaPendiente = new ArrayList<LogTiendas>();
	}
	
	public List<LogTiendas> getFechaRemanente() {
		return fechaRemanente;
	}
	public void setFechaRemanente(List<LogTiendas> fechaRemanente) {
		this.fechaRemanente = fechaRemanente;
	}
	public List<LogTiendas> getTiendaRemanente() {
		return tiendaRemanente;
	}
	public void setTiendaRemanente(List<LogTiendas> tiendaRemanente) {
		this.tiendaRemanente = tiendaRemanente;
	}
	public List<LogTiendas> getTiendaPendiente() {
		return tiendaPendiente;
	}
	public void setTiendaPendiente(List<LogTiendas> tiendaPendiente) {
		this.tiendaPendiente = tiendaPendiente;
	}
	
	public List<LogTiendas> getfechaPendiente() {
		return fechaPendiente;
	}
	public void setfechaPendiente(List<LogTiendas> fechaPendiente) {
		this.fechaPendiente = fechaPendiente;
	}

	public List<CabeceraCifra> getCabecera() {
		return cabecera;
	}

	public void setCabecera(List<CabeceraCifra> cabecera) {
		this.cabecera = cabecera;
	}
}
