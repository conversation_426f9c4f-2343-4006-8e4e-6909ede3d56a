/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.entidades;

/**
 * Clase que sirve como modelo de datos para las emrpesas homologadas.
 * <AUTHOR>
 *
 */
public class EmpresaHomologada {
	private int iduHomologacionEmpresa;
	private String claveBanco;
	private String tipoMovimientoBanco;
	private String claveCoppel;
	private String tipoMovimientoCoppel;
	private boolean activo;
	private String usuarioCreacion;
	private String usuarioModificacion;
	
	/**
	 * Constructor de la clase EmpresaHomologada.
	 */
	public EmpresaHomologada() {
		iduHomologacionEmpresa = 0;
		claveBanco = "";
		tipoMovimientoBanco = "";
		claveCoppel = "";
		tipoMovimientoCoppel = "";
		activo = true;
		usuarioCreacion = "";
		usuarioModificacion = "";
	}

	public int getIduHomologacionEmpresa() {
		return iduHomologacionEmpresa;
	}

	public void setIduHomologacionEmpresa(int iduHomologacionEmpresa) {
		this.iduHomologacionEmpresa = iduHomologacionEmpresa;
	}

	public String getClaveBanco() {
		return claveBanco;
	}

	public void setClaveBanco(String claveBanco) {
		this.claveBanco = claveBanco;
	}

	public String getTipoMovimientoBanco() {
		return tipoMovimientoBanco;
	}

	public void setTipoMovimientoBanco(String tipoMovimientoBanco) {
		this.tipoMovimientoBanco = tipoMovimientoBanco;
	}

	public String getClaveCoppel() {
		return claveCoppel;
	}

	public void setClaveCoppel(String claveCoppel) {
		this.claveCoppel = claveCoppel;
	}

	public String getTipoMovimientoCoppel() {
		return tipoMovimientoCoppel;
	}

	public void setTipoMovimientoCoppel(String tipoMovimientoCoppel) {
		this.tipoMovimientoCoppel = tipoMovimientoCoppel;
	}

	public boolean isActivo() {
		return activo;
	}

	public void setActivo(boolean activo) {
		this.activo = activo;
	}

	public String getUsuarioCreacion() {
		return usuarioCreacion;
	}

	public void setUsuarioCreacion(String usuarioCreacion) {
		this.usuarioCreacion = usuarioCreacion;
	}

	public String getUsuarioModificacion() {
		return usuarioModificacion;
	}

	public void setUsuarioModificacion(String usuarioModificacion) {
		this.usuarioModificacion = usuarioModificacion;
	}
	
	
}
