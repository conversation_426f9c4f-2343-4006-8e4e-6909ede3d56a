package com.coppel.wsconciliacion.entidades;

public class BitacoraInterfase {

	public static int idEstatusCreacion = 1;
	public static int idEstatusPositivo = 2;
	public static int idEstatusError = 3;
	
	public int idPasoCargaConfiguracion = 1;
	public int idProcesoInfoTiendas = 2;
	public int idProcesoEscribeArchivos = 3;
	public int idProcesoEnvioArchivos = 4;
	public int idInsertaTiendasConciliadas = 5;
	public int idProcesoEnviaCorreo = 6;
	public int idProcesoEliminarArchivo = 7;
	
	public static String msnCargaConfiguracion = "Inicia proceso.";
	public static String msnInfoTiendas = "Proceso de Informacion tiendas.";
	public static String msnEscribeArchivos = "Proceso Escribe archivos.";
	public static String msnEnvioArchivos = "Envio archivos al servidor.";
	public static String msnTiendasConciliadas = "Inserta y actualiza tiendas conciliadas.";
	public static String msnEnviaCorreo = "Proceso de enviar correo.";
	public static String msnEliminaArchivo = "Proceso de eliminar archivos.";
		
	public static String msnErrorCargaConfiguracion = "Fallo la carga de configuracion.";
	public static String msnErrorobtenerconfigBD = "Error al obtener configuracion de BD.";
	public static String msnErrorSinRegistrosGeneraArchivo = "No encontro registros para archivo de conciliacion.";
	public static String msnErrorEscribirArchivoConciliacion = "Error al escribir archivo de conciliacion.";
	public static String msnErrorGenerarCibrasControl = "Error al generar cifras de control.";
	public static String msnErrorEnviarArchivo = "Error al enviar archivo.";
	public static String msnErrorActuaizarInformacion = "Error al actualizar informacion.";
	public static String msnErrorEnviarCorreo = "Error al enviar el correo.";
	public static String msnErrorEliminarArchivos = "Error al eliminar los archivos temporales.";
	
	
	public int iduBitacora;
	public int iduPaso;
	public int iduEstatus;
	public String mensajeError;
	public String iduUsuario;
	
	public BitacoraInterfase(String idUsuario) {
		this.iduUsuario = idUsuario;
	}

}