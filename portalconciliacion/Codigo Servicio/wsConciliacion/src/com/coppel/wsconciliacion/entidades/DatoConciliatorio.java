/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.entidades;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Clase que sirve como el modelo de datos para los movimientos que conciliaran.
 * <AUTHOR>
 *
 */
public class DatoConciliatorio implements Comparable<DatoConciliatorio>{
	private String clvDatoConci;
	private String tipoMovimiento;
	private String importe;
	private Date fechaMovimiento;
	private int numTienda;
	private String efectuo;
	private String claveEmpresa;
	private String ciudad;
	private String descripcionPago;
	private String caja;
	private String folio;
	private String recibo;
	private String contrato;
	private String campoFuturo1;
	private String campoFuturo2;
	private String campoFuturo3;
	private String campoFuturo4;
	
	/**
	 * Constructor de la clase DatoConciliatorio.
	 */
	public DatoConciliatorio(){
		clvDatoConci = "";
		tipoMovimiento = "";
		importe = "0";
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try{
			fechaMovimiento =
					dateFormat.parse("1990-01-01 00:00:00");
		}
		catch(ParseException e){
			e.getMessage();
		}
		numTienda = 0;
		efectuo = "";
		claveEmpresa = "";
		ciudad = "";
		descripcionPago = "";
		caja = "";
		folio = "";
		recibo = "";
		contrato = "";
		campoFuturo1 = "";
		campoFuturo2 = "";
		campoFuturo3 = "";
		campoFuturo4 = "";
	}

	public String getClvDatoConci() {
		return clvDatoConci;
	}

	public void setClvDatoConci(String clvDatoConci) {
		this.clvDatoConci = clvDatoConci;
	}

	public String getTipoMovimiento() {
		return tipoMovimiento;
	}

	public void setTipoMovimiento(String tipoMovimiento) {
		this.tipoMovimiento = tipoMovimiento;
	}

	public String getImporte() {
		return importe;
	}

	public void setImporte(String importe) {
		this.importe = importe;
	}

	public Date getFechaMovimiento() {
		return fechaMovimiento;
	}
	public String getFechaMovimientoString() {
            DateFormat formato = new SimpleDateFormat("yyyy-MM-dd");
            String fecha = formato.format(getFechaMovimiento());
            return fecha;		
	}
	public void setFechaMovimiento(Date fechaMovimiento) {
		this.fechaMovimiento = fechaMovimiento;
	}

	public int getNumTienda() {
		return numTienda;
	}

	public void setNumTienda(int numTienda) {
		this.numTienda = numTienda;
	}

	public String getEfectuo() {
		return efectuo;
	}

	public void setEfectuo(String efectuo) {
		this.efectuo = efectuo;
	}

	public String getClaveEmpresa() {
		return claveEmpresa;
	}

	public void setClaveEmpresa(String claveEmpresa) {
		this.claveEmpresa = claveEmpresa;
	}

	public String getCiudad() {
		return ciudad;
	}

	public void setCiudad(String ciudad) {
		this.ciudad = ciudad;
	}

	public String getCaja() {
		return caja;
	}

	public void setCaja(String caja) {
		this.caja = caja;
	}

	public String getFolio() {
		return folio;
	}

	public void setFolio(String folio) {
		this.folio = folio;
	}

	public String getRecibo() {
		return recibo;
	}

	public void setRecibo(String recibo) {
		this.recibo = recibo;
	}

	public String getContrato() {
		return contrato;
	}

	public void setContrato(String contrato) {
		this.contrato = contrato;
	}

	public String getCampoFuturo1() {
		return campoFuturo1;
	}

	public void setCampoFuturo1(String campoFuturo1) {
		this.campoFuturo1 = campoFuturo1;
	}

	public String getCampoFuturo2() {
		return campoFuturo2;
	}

	public void setCampoFuturo2(String campoFuturo2) {
		this.campoFuturo2 = campoFuturo2;
	}

	public String getCampoFuturo3() {
		return campoFuturo3;
	}

	public void setCampoFuturo3(String campoFuturo3) {
		this.campoFuturo3 = campoFuturo3;
	}

	public String getCampoFuturo4() {
		return campoFuturo4;
	}

	public void setCampoFuturo4(String campoFuturo4) {
		this.campoFuturo4 = campoFuturo4;
	}

	public String getDescripcionPago() {
		return descripcionPago;
	}

	public void setDescripcionPago(String descripcionPago) {
		this.descripcionPago = descripcionPago;
	}

	@Override
	public int compareTo(DatoConciliatorio dato) {
		// TODO Auto-generated method stub
		if (getFechaMovimiento() == null || dato.getFechaMovimiento() == null){
			return 0;
		}    
		return getFechaMovimiento().compareTo(dato.getFechaMovimiento());
	}
	
	@Override
	public boolean equals(Object obj){
		DatoConciliatorio datoConciliatorio = (DatoConciliatorio)obj;
		if(caja.equals(datoConciliatorio.caja)&& clvDatoConci.equals(datoConciliatorio.clvDatoConci)
				&& tipoMovimiento.equals(datoConciliatorio.tipoMovimiento)
				&& importe.equals(datoConciliatorio.importe)
				&& recibo.equals(datoConciliatorio.recibo)
				&& numTienda == datoConciliatorio.numTienda
				&& folio.equals(datoConciliatorio.folio)){
			return true;
			
		}
		return false;
	
	}
	
	@Override
	public int hashCode(){
		return(this.caja.hashCode() + this.clvDatoConci.hashCode() + this.tipoMovimiento.hashCode()
				+ this.importe.hashCode() + this.recibo.hashCode() + numTienda + this.folio.hashCode());
	}
	

}
