/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.entidades;

/**
 * Clase que sirve como modelo de datos para los campos que se encuentran en la tabla configuracion
 * <AUTHOR>
 *
 */
public class Configuracion {
	private int iduConfiguracion;
	private String descripcion;
	private String valor1;
	private String valor2;
	
	/**
	 * Constructor de la clase Configuracion
	 */
	public Configuracion(){
		iduConfiguracion = 0;
		descripcion = "";
		valor1 = "";
		valor2 = "";
	}

	public int getIduConfiguracion() {
		return iduConfiguracion;
	}

	public void setIduConfiguracion(int iduConfiguracion) {
		this.iduConfiguracion = iduConfiguracion;
	}

	public String getDescripcion() {
		return descripcion;
	}

	public void setDescripcion(String descripcion) {
		this.descripcion = descripcion;
	}

	public String getValor1() {
		return valor1;
	}

	public void setValor1(String valor1) {
		this.valor1 = valor1;
	}

	public String getValor2() {
		return valor2;
	}

	public void setValor2(String valor2) {
		this.valor2 = valor2;
	}
	
	@Override    
	public boolean equals(Object configuracion) {
	    return (configuracion instanceof Configuracion) 
	         ? getIduConfiguracion() == (((Configuracion) configuracion).getIduConfiguracion())
	         : (configuracion == this);
	}
}
