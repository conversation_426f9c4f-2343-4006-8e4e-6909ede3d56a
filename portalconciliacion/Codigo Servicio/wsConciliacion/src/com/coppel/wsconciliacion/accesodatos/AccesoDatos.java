/**
 * Copyright (c) APinterfaces S.A de C.V All rights reserved.23/11/2016
 */
package com.coppel.wsconciliacion.accesodatos;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLXML;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


import com.coppel.wsconciliacion.conexion.ConexionConciliacion;
import com.coppel.wsconciliacion.entidades.BitacoraInterfase;
import com.coppel.wsconciliacion.entidades.CabeceraCifra;
import com.coppel.wsconciliacion.entidades.CifraControl;
import com.coppel.wsconciliacion.entidades.Configuracion;
import com.coppel.wsconciliacion.entidades.EmpresaHomologada;
import com.coppel.wsconciliacion.entidades.LogTiendas;
import com.coppel.wsconciliacion.enumerador.EnumConfiguracion;

/**
 * Clase que crea, consulta, actualiza y elimina registros
 * de una tabla en una base de datos.
 * <AUTHOR>
 *
 */
public class AccesoDatos {
	int Ingresos;
	Connection conIngresos = null;
	String driver = "org.postgresql.Driver";
	Statement stmt;
	PreparedStatement preStmt;
	int proceso = 0;
	
	/**
	 * Constructor de la clase de AccesoDatos, ademas asigna los valores de los
	 * Enumconfiguracion a las variables Cajas e Ingresos que determinan en que
	 * base de datos se realiza la connecion.
	 */
	public AccesoDatos(int proceso){
		this.proceso = proceso;
		Ingresos = EnumConfiguracion.BaseIngresos.value;
	}
	
	/**
	 * Metodo para conectar a la base de datos de ingresos.
	 * @throws Exception 
	 * @throws SQLException 
	 * @throws ClassNotFoundException 
	 */
	public void conectarIngresos(boolean cerrar) throws Exception{
		try{
			if(proceso!=2){
				ConexionConciliacion.escribirLog(proceso,"--Entra al metodo conectarIngresos --");
			}
			ConexionConciliacion conexionConciliacion = new ConexionConciliacion();
			conIngresos = conexionConciliacion.LeerArchivoConexion(Ingresos);
		}
		catch(ClassNotFoundException | SQLException  e){
			ConexionConciliacion.escribirLog(proceso,"-- error en conectarIngresos => " + e.getMessage() + "--");
			throw e;
		}finally{	
			if(cerrar) {
				desconectarIngresos();	
			}
		}
	}
	
	/**
	 * Metodo para cerrar el statement.
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 * @throws SQLException 
	 */
	public void cerrarSentencia() throws SecurityException, NullPointerException, IOException{
		if(proceso!=2){
			ConexionConciliacion.escribirLog(proceso,"--Entra al metodo cerrarSentencia --");
		}
		
		try{
			if(stmt != null){
				if(!stmt.isClosed()){
					stmt.close();
				}
			}
			if(preStmt != null){
				if(!preStmt.isClosed()){
					preStmt.close();
				}
			}
		}
		catch(SQLException e){
			try {
				ConexionConciliacion.escribirLog(proceso,"-- cerrarSentencia Error => " + e.getMessage());
				throw e;
			} catch (SQLException e1) {	
				ConexionConciliacion.escribirLog(proceso,"-- cerrarSentencia Error => " + e1.getMessage());
				e1.getMessage();
			}
		}
	}
	
	/**
	 * Metodo para desconectar la base de datos de ingresos.
	 * @throws SQLException 
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public void desconectarIngresos() throws SQLException, SecurityException, NullPointerException, IOException{
		try{
			if(proceso!=2){
				ConexionConciliacion.escribirLog(proceso,"--Entra al metodo desconectarIngresos --");
			}
			
			if(conIngresos != null){
				if(!conIngresos.isClosed()){
					conIngresos.close();
				}
			}
		}
		catch(SQLException e){
			ConexionConciliacion.escribirLog(proceso,"-- error en desconectarIngresos => " + e.getMessage() + "--");
			throw e;
		}
	}
	
	/**
	 * Metodo para obtener la configuracion de la tabla ctl_configuracion
	 * en la base de datos de ingresos.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException 
	 * @throws ClassNotFoundException 
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 * @throws Exception
	 */
	public ResultSet obtenerConfiguracionIngresos() throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException {
		if(proceso!=2){
			ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerConfiguracionIngresos --");
		}
		
		ResultSet resultado = null;
		StringBuilder queryBuilder = new StringBuilder();
		String query = null;
		try{
			Class.forName(driver);
			queryBuilder.append("SELECT idu_configuracion, des_configuracion,des_valor1,");
			queryBuilder.append("des_valor2 FROM fun_obtenerconfiguracion() ");
			query = queryBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			resultado = preStmt.executeQuery();
		}
		catch(SQLException | ClassNotFoundException e){
			ConexionConciliacion.escribirLog(proceso, "obtenerConfiguracionIngresos error ===>" + e.getMessage());
			throw e;
		}
		return resultado;
	}
	
	/**
	 * Metodo para obtener los movimientos de las tiendas de la tiecarmovhistorial,
	 * tiecontroldemovimientosadministracion en la base de datos de ingresos.
	 * @param fechaConciliacion ultima fecha en la que se realizo la conciliacion, tipo Date.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerMovimientosIngresos(String fechaConciliacion) 
			throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerMovimientosIngresos --");
		ResultSet resultado = null;
		String query = null;
		StringBuilder queryBuilder = new StringBuilder();
		try{
			Class.forName(driver);
			queryBuilder.append("SELECT tma.claveb,tma.tipomovimientob,tma.importe,tma.tienda,");
			queryBuilder.append("tma.caja,tma.ciudad,tma.contrato,tma.fol_sucursal,tma.efectuo,");
			queryBuilder.append("tma.recibo,tma.fecha,tma.des_tituloconsulta ");
			queryBuilder.append("FROM fun_obtenermovimientosingresos(?)  tma ");
			query = queryBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			preStmt.setString(1, fechaConciliacion);
			resultado = preStmt.executeQuery();
		}
		catch(SQLException | ClassNotFoundException e){
			ConexionConciliacion.escribirLog(proceso,"-- obtenerMovimientosIngresos Error => " + e.getMessage());
			throw e;
		}
		return resultado;
	}
	
	/**
	 * Metodo para obtener el total de las tiendas de la tabla tiendas 
	 * en la base de datos de ingresos.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerTotalTiendas() 
			throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		if(proceso!=2){
			ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerTotalTiendas --");
		}
		
		ResultSet resultado = null;
		try{
			Class.forName(driver);
			String query = "SELECT totaltiendas FROM fun_obtenertotaltiendas();";
			preStmt = conIngresos.prepareStatement(query);
			resultado = preStmt.executeQuery();
		}
		catch(SQLException | ClassNotFoundException e){
			ConexionConciliacion.escribirLog(proceso,"--Error al obtenerTotalTiendas" + e.getMessage() + " --");
			throw e;
		}
		return resultado;
	}
	
	/**
	 * Metodo para obtener las empresas homologadas para la conciliacion
	 * de la tabla ctl_homologacionempresas en la base de datos de ingresos.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerEmpresasHomologadas() throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerEmpresasHomologadas --");
		ResultSet resultado = null;
		String query = null;
		StringBuilder queryBuilder = new StringBuilder();
		try{
			Class.forName(driver);
			stmt = conIngresos.createStatement();
			queryBuilder.append("SELECT  idu_homologacionempresa,clv_movimientob, ");
			queryBuilder.append("clv_movimientoc, clv_tipomovimientob, clv_tipomovimientoc, ");
			queryBuilder.append("opc_activo, fec_fechacreacion,idu_usuariocreacion ");
			queryBuilder.append("FROM fun_obtenerempresashomologadas()");
			query = queryBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			resultado = preStmt.executeQuery();
		}
		catch(SQLException | ClassNotFoundException e){
			ConexionConciliacion.escribirLog(proceso,"-- obtenerEmpresasHomologadas Error => " + e.getMessage());
			throw e;
		}
		return resultado;
	}
	
	/**
	 * Metodo para obtener las tiendas remanentes de las tablas ctl_logtiendasconciliacion,
	 * tiecontroldemovimientosadministracion, ctl_homologacionempresas de la base de datos 
	 * de ingresos.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException 
	 * @throws ClassNotFoundException 
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerTiendasRemanentesIngresos() 
			throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerTiendasRemanentesIngresos --");
		ResultSet resultado = null;
		StringBuilder queryBuilder = new StringBuilder();
		String query = null;
		try{
			Class.forName(driver);
			stmt = conIngresos.createStatement();
			queryBuilder.append("SELECT  tcm.claveb, tcm.tipomovimientob,tcm.importe,tcm.tienda,");
			queryBuilder.append("tcm.caja,tcm.ciudad,tcm.contrato,tcm.fol_sucursal,tcm.efectuo,");
			queryBuilder.append("tcm.recibo,tcm.fecha,tcm.des_tituloconsulta ");
			queryBuilder.append("FROM fun_obtenertiendasremanentesingresos() tcm ");
			query = queryBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			resultado = preStmt.executeQuery();
		}
		catch(SQLException | ClassNotFoundException e){
			ConexionConciliacion.escribirLog(proceso,"-- obtenerTiendasRemanentesIngresos Error => " + e.getMessage());
			throw e;
		}
		return resultado;
	}
	
	/**
	 * Metodo para obtener las tiendas remanentes que no se lograron conciliar
	 * de la tabla ctl_logtiendasconciliacion, tiecontroldemovimientosadministracion
	 * de la base de datos de ingresos.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException 
	 * @throws ClassNotFoundException 
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerTiendasRemanentesNoConciliadas() 
			throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerTiendasRemanentesNoConciliadas --");
		ResultSet resultado = null;
		StringBuilder queryBuilder = new StringBuilder();
		String query = null;
		try{
			Class.forName(driver);
			queryBuilder.append("SELECT  num_tienda, fecha  ");
			queryBuilder.append("FROM  fun_obtenertiendasremanentesnoconciliadas()");
			query = queryBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			resultado = preStmt.executeQuery();
			stmt = conIngresos.createStatement();
		}
		catch(SQLException | ClassNotFoundException e){
			ConexionConciliacion.escribirLog(proceso,"-- obtenerTiendasRemanentesNoConciliadas Error => " + e.getMessage());
			throw e;
		}
		
		return resultado;
	}
	
	/**
	 * Metodo para obtener las tiendas que no se lograron conciliar de la
	 * tabla tiecontroldemovimientos de la base de datos de cajas.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException
	 * @throws ClassNotFoundException 
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerTiendasNoConciliadasIngresosCifra(Date fechaConciliacion) 
			throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerTiendasNoConciliadasCajasCifra --");
		ResultSet resultado = null;
		StringBuilder queryBuilder = new StringBuilder();
		String query = null;
		DateFormat fechaFormato = new SimpleDateFormat("yyyy-MM-dd");
		try{

			Class.forName(driver);
			queryBuilder.append("SELECT  tienda  ");
			queryBuilder.append("FROM fun_obtenertiendasnoconciliadasingresoscifra(?)");
			query = queryBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			preStmt.setString(1, fechaFormato.format(fechaConciliacion));
			resultado = preStmt.executeQuery();
		}
		catch(SQLException | ClassNotFoundException e){
			ConexionConciliacion.escribirLog(proceso,"-- obtenerTiendasNoConciliadasCajasCifra Error => " + e.getMessage());
			throw e;
		}
		return resultado;
	}
	
	/**
	 * Metodo para obtener las tiendas que no se lograron conciliar de la
	 * tabla tiecontroldemovimientos de la base de datos de cajas.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException
	 * @throws ClassNotFoundException 
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerTiendasNoConciliadasIngresos(Date fechaConciliacion) 
			throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerTiendasNoConciliadasIngresos --");
		ResultSet resultado = null;
		StringBuilder queryBuilder = new StringBuilder();
		String query = null;
		DateFormat fechaFormato = new SimpleDateFormat("yyyy-MM-dd");
		try{
			Class.forName(driver);
			queryBuilder.append("SELECT  tienda, fecha ");
			queryBuilder.append("FROM fun_obtenertiendasnoconciliadasingresos(?) ");
			query = queryBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			preStmt.setString(1, fechaFormato.format(fechaConciliacion));
			resultado = preStmt.executeQuery();
		}
		catch(SQLException | ClassNotFoundException e){
			ConexionConciliacion.escribirLog(proceso,"-- obtenerTiendasNoConciliadasIngresos Error => " + e.getMessage());
			throw e;
		}
		return resultado;
	}
	
	/**
	 * Metodo para obtener el total de tiendas que  se lograron conciliar de la
	 * tabla tiecontroldemovimientos de la base de datos de cajas.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException
	 * @throws ClassNotFoundException 
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerTiendasProcesadasCajas(Date fechaConciliacion) 
			throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerTiendasProcesadasCajas --");
		
		ResultSet resultado = null;
		StringBuilder queryBuilder = new StringBuilder();
		String query = null;
		DateFormat fechaFormato = new SimpleDateFormat("yyyy-MM-dd");
		try{
			Class.forName(driver);
			queryBuilder.append("SELECT  totaltiendas, fecha ");
			queryBuilder.append("FROM fun_obtenertotaltiendasprocesadas(?) ");
			query = queryBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			preStmt.setString(1, fechaFormato.format(fechaConciliacion));
			resultado = preStmt.executeQuery();
		}
		catch(SQLException | ClassNotFoundException e){
			ConexionConciliacion.escribirLog(proceso,"-- obtenerTiendasProcesadasCajas Error => " + e.getMessage());
			throw e;
		}
		return resultado;
	}
	
	/**
	 * Metodo para obtener las tiendas que no se lograron conciliar
	 * de la tabla tiecontroldemovimientosadministracion de la
	 * base de datos de ingresos.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException 
	 * @throws ClassNotFoundException 
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerTiendasNoConciliadasIngresos(String fechaConciliacion) 
			throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerTiendasNoConciliadasIngresos --");
		ResultSet resultado = null;
		StringBuilder queryBuilder = new StringBuilder();
		String query = null;
		try{
			Class.forName(driver);
			queryBuilder.append("SELECT tienda,fecha ");
			queryBuilder.append("FROM  fun_obtenertiendasnoconciliadasingresos(?) ");
			query = queryBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			preStmt.setString(1, fechaConciliacion);
			resultado = preStmt.executeQuery();
		}
		catch(SQLException | ClassNotFoundException e){
			ConexionConciliacion.escribirLog(proceso,"-- obtenerTiendasNoConciliadasIngresos Error => " + e.getMessage());
			throw e;
		}
		return resultado;
	}
	
	/**
	 * Metodo para obtener las tiendas remanentes de la tabla tiecarmovhistorial,
	 * tiecontroldemovimientos de la base de datos de cajas
	 * @param empresasHomologadas recibe una lista del modelo de datos de EmpresaHomologada
	 * el cual se usa para saber que movimientos exactamente se obtendran.
	 * @param tiendasPendiente recibe una lista del modelo de datos de LogTiendas el cual
	 * se usa para obtener las movimientos mediante el numero de tienda y la fecha faltante.
	 * @return un ResultSet con los datos consultados.
	 * @throws ClassNotFoundException 
	 * @throws SQLException 
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerTiendasConCeroMovimientos(Date fechaAntigua, List<LogTiendas> tiendasRemanenteConciliada)
			throws ClassNotFoundException, SQLException, SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerTiendasConCeroMovimientos --");
		ResultSet resultado = null;
		SQLXML xmlTienda;
		StringBuilder xmlBuilder = new StringBuilder();
		StringBuilder queryBuilder = new StringBuilder();
		String query = "";
		DateFormat fechaFormato = new SimpleDateFormat("yyyy-MM-dd");
		try{
			Class.forName(driver);
			queryBuilder.append("SELECT   tienda, fecha  ");
			queryBuilder.append("FROM  fun_obtenertiendasconceromovimientos(?,?) ");
			
			if(tiendasRemanenteConciliada.size() > 0){
				xmlBuilder = new StringBuilder();
				xmlTienda = conIngresos.createSQLXML();
				xmlBuilder.append("<ROOT xmlns=\"http://conciliacion.coppel.com\"> ");
				for(int i = 0; i < tiendasRemanenteConciliada.size(); i++){
					xmlBuilder.append("<Tienda>");
					xmlBuilder.append("<num_tienda>");
					xmlBuilder.append(tiendasRemanenteConciliada.get(i)
							.getNumTienda());
					xmlBuilder.append("</num_tienda>");
					xmlBuilder.append("<fecha>");
					xmlBuilder.append(tiendasRemanenteConciliada.get(i)
							.getFechaConciliacion());
					xmlBuilder.append("</fecha>");
					xmlBuilder.append("</Tienda>");
				}
				xmlBuilder.append("</ROOT>");
				xmlTienda.setString(xmlBuilder.toString());
			}
			else{
				xmlBuilder = new StringBuilder();
				xmlTienda = conIngresos.createSQLXML();
				xmlBuilder.append("<ROOT xmlns=\"http://conciliacion.coppel.com\"> ");
				xmlBuilder.append("<Tienda>");
				xmlBuilder.append("<num_tienda>");
				xmlBuilder.append("0");
				xmlBuilder.append("</num_tienda>");
				xmlBuilder.append("<fecha>");
				xmlBuilder.append("1999-01-01");
				xmlBuilder.append("</fecha>");
				xmlBuilder.append("</Tienda>");
				xmlBuilder.append("</ROOT>");
				xmlTienda.setString(xmlBuilder.toString());
			}
			query = queryBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			preStmt.setString(1, fechaFormato.format(fechaAntigua));
			preStmt.setSQLXML(2, xmlTienda);
			resultado = preStmt.executeQuery();
		}
		catch(ClassNotFoundException | SQLException e){
			ConexionConciliacion.escribirLog(proceso,"-- metodo obtenerTiendasConCeroMovimientos error => " + e.getMessage() + " --");
			throw e;
		}
		return resultado;
	}
	
	/**
	 * Metodo para insertar las tiendas no conciliadas en  la tabla ctl_logtiendasconciliacion
	 * de la base de datos de ingresos.
	 * @param logTiendas recibe el modelo de datos de LogTiendas, el cual posee los datos
	 * necesarios a insertar.
	 * @return un valor boolean indicando si fue insertado correctamente.
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 * @throws SQLException 
	 */
	/*
	public boolean insertarTiendasLog(List<LogTiendas> logTiendas) throws SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso," -- Entra al metodo insertarTiendasLog --");
		ResultSet resultado = null;
		boolean retorna = false;
		StringBuilder xmlBuilder = new StringBuilder();
		SQLXML xmlTienda;
		StringBuilder xmlParametro = new StringBuilder();
		String query = null;
		try{
			ConexionConciliacion.escribirLog(proceso,"-- insertarTiendasLog :: tamanio de tiendas  => "+ logTiendas.size() +" --");
			if(logTiendas.size() > 0){
				xmlTienda = conIngresos.createSQLXML(); 
				ConexionConciliacion.escribirLog(proceso,"-- SELECT fun_insertartiendaslog_xml FROM fun_insertartiendaslog_xml(?); --");
				xmlBuilder.append("SELECT fun_insertartiendaslog_xml FROM fun_insertartiendaslog_xml(?);");
				
				xmlParametro.append("<ROOT xmlns=\"http://localhost.coppel.com\"> ");
				for(int i = 0; i < logTiendas.size(); i++){
					xmlParametro.append("<Tienda>");
					xmlParametro.append("<num_tienda>");
					xmlParametro.append(logTiendas.get(i).getNumTienda());
					xmlParametro.append("</num_tienda>");
					xmlParametro.append("<fec_fechalog>");
					xmlParametro.append(logTiendas.get(i).getFechaConciliacion());
					xmlParametro.append("</fec_fechalog>");
					xmlParametro.append("<opc_conciliado>");
					xmlParametro.append("0");
					xmlParametro.append("</opc_conciliado>");
					xmlParametro.append("<opc_activo>");
					xmlParametro.append("1");
					xmlParametro.append("</opc_activo>");
					xmlParametro.append("<idu_usuariocreacion>");
					xmlParametro.append("1");
					xmlParametro.append("</idu_usuariocreacion>");
					xmlParametro.append("</Tienda>");
				}
				xmlParametro.append("</ROOT>");
				
				xmlTienda.setString(xmlParametro.toString());
				ConexionConciliacion.escribirLog(proceso,"-- insertarTiendasLog xmlTienda => "+ xmlParametro.toString() +" --");
			}
			else{
				ConexionConciliacion.escribirLog(proceso,"-- tamanio de tienda 0  --");
				return false;
			}
			
			ConexionConciliacion.escribirLog(proceso,"-- antes de ejecutar Query --");
			query = xmlBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			preStmt.setSQLXML(1, xmlTienda);
			resultado = preStmt.executeQuery();
			
			if(resultado!=null) {
				ConexionConciliacion.escribirLog(proceso,"-- resultado null --");
				retorna = true;
			}
			
		} catch(SQLException ex){
			ConexionConciliacion.escribirLog(proceso,"-- insertarTiendasLog ERROR :" + ex.getMessage() + " --");
			ex.getMessage();
		}
		return retorna;
	}
	*/
	public boolean insertarTiendasLog(List<LogTiendas> logTiendas) throws SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo insertarTiendasLog --");
		boolean resultado = false;
		PreparedStatement stmt = null;
		try{
			ConexionConciliacion.escribirLog(proceso,"--Entro al de  insertarTiendasLog--");
			for(int i = 0; i < logTiendas.size(); i++){
				stmt = conIngresos.prepareStatement("SELECT * FROM fun_insertartiendaslog(?,?,?,?,?)");
				stmt.setInt(1, logTiendas.get(i).getNumTienda());
				stmt.setDate(2, (java.sql.Date) logTiendas.get(i).getFechaConciliacion());
				stmt.setInt(3, 0);
				stmt.setInt(4, 1);
				stmt.setInt(5, 1);
				stmt.execute();
			}
			ConexionConciliacion.escribirLog(proceso,"-- SELECT * FROM fun_insertartiendaslog(?,?,?,?,?) --");
			ConexionConciliacion.escribirLog(proceso,"--Resultado insertarTiendasLog => true--");
			resultado = true;
		}
		catch(SQLException ex){
			ConexionConciliacion.escribirLog(proceso,"-- metodo insertarTiendasLog error => " + ex.getMessage() + " --");
			ex.printStackTrace();
		}
		
		return resultado;
	}
	
	/**
	 * Metodo para actualizar las tiendas pendientes y remanentes en la tabla
	 * ctl_logtiendasconciliacion en la base de datos de ingresos.
	 * @param tiendasPendiente recibe una lista del modelo de datos LogTiendas de las
	 * tiendas que se actualzaran.
	 * @return un valor boolean que indica si fue actualizado correctamente.
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 * @throws SQLException 
	 */
/*
	public boolean actualizarTiendasConciliadas(List<LogTiendas> tiendaRemanente) throws SecurityException, NullPointerException, IOException, SQLException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo actualizarTiendasConciliadas --");
		ResultSet resultado = null;
		boolean retorna = false;
		StringBuilder xmlBuilder = new StringBuilder();
		SQLXML xmlTienda;
		StringBuilder xmlParametro = new StringBuilder();
		String query = null;
		 
		try{
			ConexionConciliacion.escribirLog(proceso,"--actualizarTiendasConciliadas : Entra al try --");
			DateFormat fechaFormato = new SimpleDateFormat("yyyy-MM-dd");
			ConexionConciliacion.escribirLog(proceso,"-- Tamanio de tiendas remanentes " + tiendaRemanente.size() +"--");
			if(tiendaRemanente.size() > 0){
				xmlTienda = conIngresos.createSQLXML(); 
				ConexionConciliacion.escribirLog(proceso,"-- SELECT fun_actualizartiendasconciliadas_xml FROM fun_actualizartiendasconciliadas_xml(?); --");
				xmlBuilder.append("SELECT fun_actualizartiendasconciliadas_xml FROM fun_actualizartiendasconciliadas_xml(?);");
					
				xmlParametro.append("<ROOT xmlns=\"http://localhost.coppel.com\"> ");
				for(int i = 0; i < tiendaRemanente.size(); i++){
					xmlParametro.append("<Tienda>");
					xmlParametro.append("<get_monto_total>");
					xmlParametro.append((int)tiendaRemanente.get(i).getMontoTotal());
					xmlParametro.append("</get_monto_total>");
					xmlParametro.append("<num_tienda>");
					xmlParametro.append( tiendaRemanente.get(i).getNumTienda());
					xmlParametro.append("</num_tienda>");
					xmlParametro.append("<fec_conciliacion>");
					xmlParametro.append(fechaFormato.format(tiendaRemanente.get(i).getFechaConciliacion()));
					xmlParametro.append("</fec_conciliacion>");
					xmlParametro.append("</Tienda>");
				}
				xmlParametro.append("</ROOT>");
				
				xmlTienda.setString(xmlParametro.toString());
				ConexionConciliacion.escribirLog(proceso,"-- actualizarTiendasConciliadas xmlParametro "+ xmlParametro.toString() +" --");
			}
			else{
				ConexionConciliacion.escribirLog(proceso,"-- tiendas remanentes 0 --");
				return false;
			}
			
			query = xmlBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			preStmt.setSQLXML(1, xmlTienda);
			resultado = preStmt.executeQuery();
			
			if(resultado!=null)
				retorna = true;
		}
		catch(SQLException ex){
			ConexionConciliacion.escribirLog(proceso,"--actualizarTiendasConciliadas : Entra al catch --");
			ConexionConciliacion.escribirLog(proceso,"-- actualizarTiendasConciliadas ERROR :" + ex.getMessage() + " --");
			ex.getMessage();
		}finally {
			ConexionConciliacion.escribirLog(proceso,"--actualizarTiendasConciliadas : Entra al finally --");
			try {
				ConexionConciliacion.escribirLog(proceso,"--actualizarTiendasConciliadas : Entra al try del finally --");
				if (conIngresos != null) {
					conIngresos.close();
				}
			} catch (SQLException e) {
				ConexionConciliacion.escribirLog(proceso,"--actualizarTiendasConciliadas : Entra al catch del try del finally --");
				ConexionConciliacion.escribirLog(proceso,"-- actualizarTiendasConciliadas ERROR SQLException:" + e.getMessage() + " --");
				throw e;
			}			
		}
		
		return retorna;
	}
	*/
	public boolean actualizarTiendasConciliadas(List<LogTiendas> tiendaRemanente) throws SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo actualizarTiendasConciliadas --");
		boolean resultado = false;
		String query = "SELECT * FROM fun_actualizartiendasconciliadas(?,?,?)";
		DateFormat fechaFormato = new SimpleDateFormat("yyyy-MM-dd");
		PreparedStatement stmt = null;
		try {
			ConexionConciliacion.escribirLog(proceso,"--Entra al try actualizarTiendasConciliadas --");
			stmt = conIngresos.prepareStatement(query);
			for(int i = 0; i < tiendaRemanente.size(); i++){
				stmt.setInt(1, (int)tiendaRemanente.get(i).getMontoTotal());
				stmt.setInt(2, tiendaRemanente.get(i).getNumTienda());
				stmt.setString(3, fechaFormato.format(tiendaRemanente.get(i)
						.getFechaConciliacion()));
				stmt.execute();
			}
			ConexionConciliacion.escribirLog(proceso,"-- SELECT * FROM fun_actualizartiendasconciliadas(?,?,?) --");
			ConexionConciliacion.escribirLog(proceso,"-- resultado actualizarTiendasConciliadas => true --");
			resultado = true;
		}
		catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			ConexionConciliacion.escribirLog(proceso,"-- metodo actualizarTiendasConciliadas error => " + e.getMessage() + " --");
			
		}
		return resultado;
	}

	/**
	 * Metodo para guardar los datos de la cifra de control.
	 * @param cifraControl recibe un modelo de datos del tipo CifraControl.
	 * tiendas que se actualzaran.
	 * @return un valor boolean que indica si se guarda correctamente.
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 * @throws SQLException 
	 */
	public boolean insertarCifra(CifraControl cifraControl, List<LogTiendas> tiendasNoConciliadas,
			Configuracion configuracion) throws SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo insertarCifra --");
		boolean resultado = false;
		CabeceraCifra cabecera;
		PreparedStatement stmt = null;
		try{
			ConexionConciliacion.escribirLog(proceso,"-- Entra al metodo try : insertarCifra --");
			for(int i = 0; i < cifraControl.getCabecera().size(); i++){
				cabecera = cifraControl.getCabecera().get(i);
				stmt = conIngresos.prepareStatement("SELECT * FROM fun_insertarcifra(?,?,?,?);");
				stmt.setInt(1, cabecera.getTiendasProcesadas());
				stmt.setInt(2, cabecera.getMontoDelDia());
				stmt.setInt(3,(int)cabecera.getPorcentaje());
				stmt.setDate(4, (java.sql.Date) cabecera.getDiaConciliado());
				stmt.execute();
			}
			ConexionConciliacion.escribirLog(proceso,"Antes de actualizar los datos");
			if(actualizarTiendasConciliadas(cifraControl.getTiendaRemanente())){
				ConexionConciliacion.escribirLog(proceso,"Actualizo conciliaciones");
				if(insertarTiendasLog(tiendasNoConciliadas)){
					ConexionConciliacion.escribirLog(proceso,"ingreso conciliaciones");
					if(actualizarFechaConciliacion(configuracion)){
						conIngresos.commit();
						resultado = true;
					}
				}
			}
		}
		catch(SQLException ex){
			ConexionConciliacion.escribirLog(proceso,"--insertarCifra Error  => " + ex.getMessage() + "--");
			ex.getMessage();
			try {
				conIngresos.rollback();
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				ConexionConciliacion.escribirLog(proceso,"--insertarCifra Error  => " + e.getMessage() + " --");
				e.getMessage();
			}
		}
		try {
			if(stmt != null){
				stmt.close();
			}
			conIngresos.close();
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			ConexionConciliacion.escribirLog(proceso,"--insertarCifra Error  => " + e.getMessage() + " --");
			e.getMessage();
		}
		return resultado;
	}
	
	/**
	 * Metodo para actualizar la utlima fecha de conciliacion en la tabla
	 * ctl_configuracion en la base de datos de ingresos.
	 * @param configuracion recibe un modelo de datos con los datos de la configuracion
	 * @return un valor boolean que indica si fue actualizado correctamente.
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 * @throws SQLException 
	 */
	public boolean actualizarFechaConciliacion(Configuracion configuracion) throws SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo actualizarFechaConciliacion --");
		boolean resultado = false;
		ConexionConciliacion.escribirLog(proceso,"-- SELECT * FROM fun_actualizarfechaconciliacion(?,?); --");
		String query = " SELECT * FROM fun_actualizarfechaconciliacion(?,?);";
		PreparedStatement stmt = null;
		try {
			stmt = conIngresos.prepareStatement(query);
			stmt.setString(1, configuracion.getValor1());
			stmt.setInt(2, configuracion.getIduConfiguracion());
			stmt.execute();
			resultado = true;
		}
		catch (SQLException e) {
			// TODO Auto-generated catch block
			ConexionConciliacion.escribirLog(proceso,"--actualizarFechaConciliacion Error  => " + e.getMessage() + " --");
			e.getMessage();
		}
		return resultado;
	}
	
	/**
	 * Metodo para obtener la  fecha  mas vieja de la tienda pendiente.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException
	 * @throws ClassNotFoundException 
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerUltimaFechaPendiente() throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso,"--Entra al metodo obtenerUltimaFechaPendiente --");
		ResultSet resultado = null;
		try{
			Class.forName(driver);
			stmt = conIngresos.createStatement();
			resultado = stmt.executeQuery("SELECT fec_fechalog FROM fun_obtenerultimafechapendiente()");
			
		}
		catch(SQLException | ClassNotFoundException e){
			ConexionConciliacion.escribirLog(proceso,"--obtenerUltimaFechaPendiente Error  => " + e.getMessage() + " --");
			throw e;
		}
		
		return resultado;
	}
	
	/**
	 * Metodo para obtener la lista de tiendas virtuales que no se deben de conciliar.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException
	 * @throws ClassNotFoundException 
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerListaTiendasVirtuales() throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		ConexionConciliacion.escribirLog(proceso, "--Entra al metodo obtenerListaTiendasVirtuales--");
		ResultSet resultado = null;
		try {
			Class.forName(driver);
			stmt = conIngresos.createStatement();
			resultado = stmt.executeQuery("SELECT tienda FROM fun_obtenerlistatiendasvirtuales()");
		} catch (SQLException | ClassNotFoundException e) {
			ConexionConciliacion.escribirLog(proceso,"--obtenerListaTiendasVirtuales Error  => " + e.getMessage() + " --");
			throw e;
		}
		
		return resultado;
	}
		
	/**
	 * Metodo para actualizar el estado de la conciliacion en la tabla
	 * bit_edoconciliacion en la base de datos de ingresos.
	 * @return un valor boolean que indica si fue actualizado correctamente.
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 * @throws SQLException 
	 */
	public boolean actualizarBitacoraConciliacion(boolean nuevaBitacora,String mensaje) throws SecurityException, NullPointerException, IOException, SQLException{
		ConexionConciliacion.escribirLog(proceso,"--actualizarBitacoraConciliacion --");
		boolean resultado = false;
		String query = "SELECT * FROM fun_actualizarbitedoconciliacion(?)";
		if(nuevaBitacora){
			query = "SELECT * FROM fun_insertarbitedoconciliacion(?)";
		}
		PreparedStatement stmt = null;
		try {
			stmt = conIngresos.prepareStatement(query);
			stmt.setString(1, mensaje);
			stmt.execute();
			conIngresos.commit();
			resultado = true;
		}
		catch (SQLException e) {
			// Se revierte la transacción en caso de error
			if (conIngresos != null) {
				conIngresos.rollback();
			}
			e.getMessage();
			ConexionConciliacion.escribirLog(proceso,"--" + e.getMessage() + " --");
			
		}finally {
			try {
				if (conIngresos != null) {
					conIngresos.close();
				}
			} catch (SQLException e) {
				ConexionConciliacion.escribirLog(proceso,"--actualizarBitacoraConciliacion Error  => " + e.getMessage() + " --");
				ConexionConciliacion.escribirLog(proceso, "Se produjo un error durante la ejecución de la consulta SQL.");
				throw e;
			}						
		}
		return resultado;
	}
	
	/**
	 * Metodo para obtener el mensaje de estado de la conciliacion
	 * de la tabla bit_edoconciliacion en la base de datos de ingresos.
	 * @return un ResultSet con los datos consultados.
	 * @throws SQLException
	 * @throws ClassNotFoundException
	 * @throws IOException 
	 * @throws NullPointerException 
	 * @throws SecurityException 
	 */
	public ResultSet obtenerBitEdoConciliacion() throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
		ResultSet resultado = null;
		String query = null;
		StringBuilder queryBuilder = new StringBuilder();
		try{
			Class.forName(driver);
			stmt = conIngresos.createStatement();
			queryBuilder.append("SELECT  idu_bitedoconciliacion,des_mensaje, ");
			queryBuilder.append("fec_fechaconciliacion,");
			queryBuilder.append("opc_activo, fec_fechacreacion,idu_usuariocreacion ");
			queryBuilder.append("FROM fun_obtenerbitedoconciliacion()");
			query = queryBuilder.toString();
			preStmt = conIngresos.prepareStatement(query);
			resultado = preStmt.executeQuery();
		}
		catch(SQLException | ClassNotFoundException e){
			throw e;
		}finally {
			try {
				if (conIngresos != null) {
					conIngresos.close();
				}
			} catch (SQLException e) {
				ConexionConciliacion.escribirLog(proceso, "Se produjo un error durante la ejecución de la consulta SQL.");
				ConexionConciliacion.escribirLog(proceso,"--obtenerBitEdoConciliacion Error  => " + e.getMessage() + " --");
				throw e;
			}			
			desconectarIngresos();
		}
		
		return resultado;
	}
	
    /**
    * Metodo guardar los datos en la tabla ctl_conciliacionpagodetalle.
    * @param sTextoGuardar
    * @return un ResultSet con los datos consultados.
    * @throws SQLException
    * @throws ClassNotFoundException
    * @throws IOException 
    * @throws NullPointerException 
    * @throws SecurityException 
    */
   public boolean guardarConciliacionPagoDetalle(String sTextoGuardar) throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
           ConexionConciliacion.escribirLog(proceso,"-- guardar Ctl_conciliacionpagodetalle --");
           boolean resultado = false;
           String query = "SELECT * FROM fun_guardarconciliacionpagodetalle(?)";

           PreparedStatement stmt = null;
           try {
				stmt = conIngresos.prepareStatement(query);
				stmt.setString(1, sTextoGuardar);
				stmt.execute();
				conIngresos.commit();
				resultado = true;
           }catch (SQLException e) {
				// Se revierte la transacción en caso de error
				if (conIngresos != null) {
					conIngresos.rollback();
				}
				e.getMessage();
				ConexionConciliacion.escribirLog(proceso,"--" + e.getMessage() + " --");
           }finally {
				try {
					if (conIngresos != null) {
						conIngresos.close();
					}
				} catch (SQLException e) {
					ConexionConciliacion.escribirLog(proceso, "Se produjo un error durante la ejecución de la consulta SQL.");
					throw e;
				}			
			desconectarIngresos();
		}
        return resultado;
   }

    /**
    * Metodo guardar los datos en la tabla ctl_conciliacionpagodetalle.
    * @param sTextoGuardar
    * @return un ResultSet con los datos consultados.
    * @throws SQLException
    * @throws ClassNotFoundException
    * @throws IOException 
    * @throws NullPointerException 
    * @throws SecurityException 
    */
    public boolean guardarConciliacionCifraPagoDetalle(String sTextoGuardar) throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
            ConexionConciliacion.escribirLog(proceso,"-- guardar Ctl_conciliacioncifrapagodetalle --");
            boolean resultado = false;
            String query = "SELECT * FROM fun_guardarconciliacioncifrapagodetalle(?)";           
            try {
            	PreparedStatement stmt = null;
                stmt = conIngresos.prepareStatement(query);
                stmt.setString(1, sTextoGuardar);
                stmt.execute();
                conIngresos.commit();
                resultado = true;
            }
            catch (SQLException e) {
                // TODO Auto-generated catch block
				// Se revierte la transacción en caso de error
				if (conIngresos != null) {
					conIngresos.rollback();
				}
                e.getMessage();
                ConexionConciliacion.escribirLog(proceso,"--" + e.getMessage() + " --");
            }
            return resultado;
    }
    
    public boolean iniciarBitacora(BitacoraInterfase interfaz)  throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException {
		try {    	
			conectarIngresos(false);
			PreparedStatement stmt = conIngresos.prepareStatement(
					"SELECT id_conciliacion,id_paso FROM fun_grabar_historico_ws_conciliaciones("
							+ "?::INTEGER, ?::VARCHAR, ?::VARCHAR);");
			stmt.setInt(1, 1);
			stmt.setString(2, "Inicializa el proceso WSConciliacion");
			stmt.setString(3, interfaz.iduUsuario);

			ResultSet informacion = stmt.executeQuery();

			int[] iarrayPasos = new int[6];
			int i =0;

			if(informacion != null) {
				while (informacion.next()) {
					iarrayPasos[i] = informacion.getInt("id_paso");
					interfaz.iduBitacora = informacion.getInt("id_conciliacion");
					i++;
				}
			}else {
				return false;
			}			
			interfaz.idPasoCargaConfiguracion = iarrayPasos[0];
			interfaz.idProcesoInfoTiendas = iarrayPasos[1];
			interfaz.idProcesoEscribeArchivos = iarrayPasos[2];
			interfaz.idProcesoEnvioArchivos = iarrayPasos[3];
			interfaz.idInsertaTiendasConciliadas = iarrayPasos[4];
			interfaz.idProcesoEnviaCorreo = iarrayPasos[5];
			
			conIngresos.commit();
		} catch (Exception e) {			
			if (conIngresos != null) {
				conIngresos.rollback();
			}
            e.getMessage();
            ConexionConciliacion.escribirLog(proceso,"-- Hubo un error al iniciar la bitacora, error: " + e.getMessage() + " --");
            return false;
		}finally {	
				if (conIngresos != null) {
					conIngresos.close();
				}		
				desconectarIngresos();
		}
		return true;
	}
 // Metodo para actualizar en la bitacora de base de datos el estatus del proceso ejecucion
	public void actualizarEstatusBitacora(int estatus, String mensaje, BitacoraInterfase interfaz)
			 throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException {

		try {
			conectarIngresos(false);
			PreparedStatement stmt;
			stmt = conIngresos.prepareStatement(
					"SELECT fun_actualizar_historico_ws_conciliaciones FROM fun_actualizar_historico_ws_conciliaciones(?::INTEGER, ?::INTEGER, ?::VARCHAR);");
			stmt.setInt(1, interfaz.iduBitacora);
			stmt.setInt(2, estatus);
			stmt.setString(3, mensaje);

			stmt.executeQuery();
			conIngresos.commit();

		} catch (Exception e) {
			// Se revierte la transacción en caso de error
			if (conIngresos != null) {
				conIngresos.rollback();
			}			
            ConexionConciliacion.escribirLog(proceso,"-- Hubo un error al actualizar la bitacora, error:" + e.getMessage() + " --");
		}finally {    
            if (conIngresos != null) {
                conIngresos.close();
				stmt.close();
            }       
        	desconectarIngresos();
    	}
	}

	// Metodo para grabar o actualizar en base de datos el estatus del paso en ejecucion en el detalle de la bitacora
	public void actualizarEstatusBitacoraDetalle(int idPaso, int estatus, String mensaje, BitacoraInterfase interfaz)
			 throws SQLException, ClassNotFoundException, SecurityException, NullPointerException, IOException{
			
		try {
			conectarIngresos(false);
			PreparedStatement stmt;
			stmt = conIngresos.prepareStatement(
					"SELECT fun_grabar_historico_detalle_ws_conciliacion FROM fun_grabar_historico_detalle_ws_conciliacion("
							+ "?::INTEGER, " + "?::INTEGER, " + "?::INTEGER, " + "?::VARCHAR, " + "?::VARCHAR);");
			stmt.setInt(1, interfaz.iduBitacora);
			stmt.setInt(2, idPaso);
			stmt.setInt(3, estatus);
			stmt.setString(4, mensaje);
			stmt.setString(5, interfaz.iduUsuario);
			
			stmt.executeQuery();
			conIngresos.commit();
		} catch (Exception e) {
			// Se revierte la transacción en caso de error
			if (conIngresos != null) {
				conIngresos.rollback();
			}			
            ConexionConciliacion.escribirLog(proceso,"-- Hubo un error al actualizar el detalle de la bitacora, error: " + e.getMessage() + " --");
		} finally{
			desconectarIngresos();
		}
	}
}
