-- Function: public.fun_actualizar_historico_ws_conciliaciones(integer, integer, varchar)
-- DROP FUNCTION public.fun_actualizar_historico_ws_conciliaciones(integer, integer, varchar);
CREATE OR REPLACE FUNCTION public.fun_actualizar_historico_ws_conciliaciones(integer, integer, varchar)
  RETURNS VOID AS
$BODY$
   DECLARE   
	idBitacora  ALIAS FOR $1;
	idEstatus  ALIAS FOR $2;	
    sMensaje  ALIAS FOR $3;


	BEGIN
    IF EXISTS (SELECT 1 FROM his_bitacora_conciliaciones WHERE idu_bitacora_conciliacion = idBitacora) THEN
        UPDATE his_bitacora_conciliaciones SET idu_estatus_conciliacion = idEstatus, des_descripcion = sMensaje, fec_actualizacion = current_timestamp WHERE idu_bitacora_conciliacion = idBitacora;
    END IF;
	END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;
ALTER FUNCTION public.fun_actualizar_historico_ws_conciliaciones(integer, integer, varchar)
  OWNER TO sysingresos;
GRANT EXECUTE ON FUNCTION public.fun_actualizar_historico_ws_conciliaciones(integer, integer, varchar) TO public;
GRANT EXECUTE ON FUNCTION public.fun_actualizar_historico_ws_conciliaciones(integer, integer, varchar) TO sysingresos;