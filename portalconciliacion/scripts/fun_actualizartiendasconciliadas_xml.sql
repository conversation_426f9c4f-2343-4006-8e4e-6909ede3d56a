
--SELECT fun_actualizartiendasconciliadas_xml FROM fun_actualizartiendasconciliadas_xml('<ROOT xmlns="http://localhost.coppel.com"><Tienda><get_monto_total>50120</get_monto_total><num_tienda>358</num_tienda><fec_conciliacion>2017-09-25</fec_conciliacion></Tienda></ROOT>'::xml);

--DROP FUNCTION public.fun_actualizartiendasconciliadas_xml(xml);
CREATE FUNCTION public.fun_actualizartiendasconciliadas_xml(xml) RETURNS BOOLEAN

    LANGUAGE plpgsql SECURITY DEFINER

    AS $_$

    DECLARE tienda ALIAS for $1;

BEGIN

   CREATE LOCAL TEMPORARY TABLE tmp_tienda_monto
   (
		get_monto_total CHARACTER VARYING(20),
		ntienda CHARACTER VARYING(20),
		fec_conciliacion CHARACTER VARYING(20)
   ) ON COMMIT DROP;
   
   WITH xmldata(col) AS (SELECT tienda::xml)

	INSERT INTO tmp_tienda_monto

	SELECT 

		unnest(xpath('/g:ROOT/g:Tienda/g:get_monto_total/text()', col, array[array['g','http://localhost.coppel.com']])) as get_monto_total,

		unnest(xpath('/g:ROOT/g:Tienda/g:num_tienda/text()', col, array[array['g','http://localhost.coppel.com']])) as ntienda,
		
		unnest(xpath('/g:ROOT/g:Tienda/g:fec_conciliacion/text()', col, array[array['g','http://localhost.coppel.com']])) as fec_conciliacion

	FROM xmldata;

	IF EXISTS (SELECT tmp.ntienda FROM tmp_tienda_monto AS tmp)
	THEN
		UPDATE ctl_logtiendasconciliacion
		SET opc_conciliado = CAST(1 AS BIT),
		fec_fechaconciliacion = NOW(),
		fec_fechamodificacion = NOW(),
		imp_montototal = subquery.get_monto_total::INTEGER
		FROM (	
    		SELECT get_monto_total,ntienda,fec_conciliacion
    		FROM tmp_tienda_monto tmp) AS subquery
		WHERE num_tienda = subquery.ntienda::INTEGER AND fec_fechalog = CAST(subquery.fec_conciliacion AS DATE);
  		RETURN TRUE;
	ELSE
		RETURN FALSE;
	END IF;
END;
$_$;

ALTER FUNCTION public.fun_actualizartiendasconciliadas_xml(xml) OWNER TO postgres;
