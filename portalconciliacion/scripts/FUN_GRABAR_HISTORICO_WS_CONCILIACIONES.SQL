-- Function: public.fun_grabar_historico_ws_conciliaciones(integer, text, text)

-- DROP FUNCTION public.fun_grabar_historico_ws_conciliaciones(integer, text, text);
CREATE OR REPLACE FUNCTION public.fun_grabar_historico_ws_conciliaciones(integer, text, text)
  RETURNS TABLE(id_conciliacion INTEGER,id_paso integer) AS
$BODY$
   DECLARE 
	iIduEstatusConciliacion	    ALIAS FOR $1;
	cDescripcionLog    			ALIAS FOR $2;
	iIduUsuarioRegistro			ALIAS FOR $3;
    pasos RECORD;
	idConciliacion integer;
	realizado integer;
	 BEGIN		 
		  

		realizado = 0; 
		SELECT idu_conciliacion INTO idConciliacion FROM CAT_CONCILIACIONES WHERE nom_conciliacion = 'WS Conciliaciones Proceso Interno';
	
		INSERT INTO his_bitacora_conciliaciones (idu_conciliacion,idu_estatus_conciliacion,des_descripcion,idu_usuario_registro,fec_registro,fec_actualizacion)
		VALUES (idConciliacion, iIduEstatusConciliacion,cDescripcionLog,iIduUsuarioRegistro,current_timestamp,current_timestamp) RETURNING idu_bitacora_conciliacion into realizado;
	
	FOR pasos IN 
		SELECT ccP.idu_conciliacion_paso as id_paso
		FROM CAT_CONCILIACION_PASOS AS ccP
			INNER JOIN CAT_CONCILIACION_ETAPAS AS ccE on ccP.idu_conciliacion_etapa = ccE.idu_conciliacion_etapa 
		WHERE ccE.idu_conciliacion = idConciliacion 
	loop
		id_conciliacion := realizado;
		id_paso := pasos.id_paso;
    RETURN NEXT;
END LOOP;
RETURN;
	END;	
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;
ALTER FUNCTION public.fun_grabar_historico_ws_conciliaciones(integer, text, text) OWNER TO sysingresos;
GRANT EXECUTE ON FUNCTION public.fun_grabar_historico_ws_conciliaciones(integer, text, text) TO public;
GRANT EXECUTE ON FUNCTION public.fun_grabar_historico_ws_conciliaciones(integer, text, text) TO sysingresos;
