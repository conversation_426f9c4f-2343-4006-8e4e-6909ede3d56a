-- Function: public.fun_grabar_historico_detalle_ws_conciliacion()
-- DROP FUNCTION public.fun_grabar_historico_detalle_ws_conciliacion(integer, integer, integer, text, text);

CREATE OR REPLACE FUNCTION public.fun_grabar_historico_detalle_ws_conciliacion(integer, integer, integer, text, text)
  RETURNS VOID AS
$BODY$
   DECLARE   
	iduBitacoraConciliacion     ALIAS FOR $1;
	iduConciliacionPaso         ALIAS FOR $2;
  	iIduEstatusConciliacion     ALIAS FOR $3;
	cDescripcionLog             ALIAS FOR $4;
	iduUsuarioRegistro          ALIAS FOR $5;
	
	idConciliacionEtapa INTEGER;
	BEGIN 
		
		SELECT idu_conciliacion INTO idConciliacionEtapa FROM CAT_CONCILIACIONES WHERE nom_conciliacion = 'WS Conciliaciones Proceso Interno';
	
		SELECT caE.idu_conciliacion_etapa INTO idConciliacionEtapa
		FROM CAT_CONCILIACION_ETAPAS AS caE
			INNER JOIN CAT_CONCILIACIONES AS cc
			ON caE.idu_conciliacion = cc.idu_conciliacion 
		WHERE nom_conciliacion = 'WS Conciliaciones Proceso Interno';


        IF EXISTS (SELECT 1 FROM his_bitacora_conciliacion_detalles WHERE idu_bitacora_conciliacion = iduBitacoraConciliacion AND idu_conciliacion_paso = iduConciliacionPaso AND idu_estatus_conciliacion = 1) THEN 
            UPDATE his_bitacora_conciliacion_detalles SET idu_estatus_conciliacion = iIduEstatusConciliacion, des_log = des_log || cDescripcionLog, fec_actualizacion = current_timestamp WHERE idu_bitacora_conciliacion = iduBitacoraConciliacion AND idu_estatus_conciliacion = 1; 
        ELSE 
            INSERT INTO his_bitacora_conciliacion_detalles (idu_bitacora_conciliacion, idu_conciliacion_etapa, idu_conciliacion_paso,idu_estatus_conciliacion, des_log, idu_usuario_registro,fec_registro,fec_actualizacion)
            VALUES (iduBitacoraConciliacion, idConciliacionEtapa, iduConciliacionPaso, iIduEstatusConciliacion, cDescripcionLog, iduUsuarioRegistro,current_timestamp,current_timestamp);
        END IF;
	END;	
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;
ALTER FUNCTION public.fun_grabar_historico_detalle_ws_conciliacion(integer, integer, integer, text, text)
  OWNER TO sysingresos;
GRANT EXECUTE ON FUNCTION public.fun_grabar_historico_detalle_ws_conciliacion(integer, integer, integer, text, text) TO public;
GRANT EXECUTE ON FUNCTION public.fun_grabar_historico_detalle_ws_conciliacion(integer, integer, integer, text, text) TO sysingresos;