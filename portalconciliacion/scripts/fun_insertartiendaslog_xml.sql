--SELECT fun_insertartiendaslog_xml FROM fun_insertartiendaslog_xml('<ROOT xmlns="http://localhost.coppel.com"><Tienda><num_tienda>7942</num_tienda><fec_fechalog>2023-09-09</fec_fechalog><opc_conciliado>0</opc_conciliado><opc_activo>1</opc_activo><idu_usuariocreacion>1</idu_usuariocreacion></Tienda></ROOT>'::xml);

--DROP FUNCTION public.fun_insertartiendaslog_xml(xml);

CREATE FUNCTION public.fun_insertartiendaslog_xml(xml) RETURNS BOOLEAN

    LANGUAGE plpgsql SECURITY DEFINER

    AS $_$

    DECLARE tienda ALIAS for $1;

BEGIN

   CREATE LOCAL TEMPORARY TABLE tmp_tienda

   (
		tienda CHARACTER VARYING(20),
		fecha CHARACTER VARYING(20),
		opc_conci CHARACTER VARYING(20),
		opc_acti CHARACTER VARYING(20),
		idu_usua CHARACTER VARYING(20)
   ) ON COMMIT DROP;
   
   WITH xmldata(col) AS (SELECT tienda::xml)

	INSERT INTO tmp_tienda

	SELECT 

		unnest(xpath('/g:ROOT/g:Tienda/g:num_tienda/text()', col, array[array['g','http://localhost.coppel.com']])) as tienda,

		unnest(xpath('/g:ROOT/g:Tienda/g:fec_fechalog/text()', col, array[array['g','http://localhost.coppel.com']])) as fecha,
		
		unnest(xpath('/g:ROOT/g:Tienda/g:opc_conciliado/text()', col, array[array['g','http://localhost.coppel.com']])) as opc_conci,
				
		unnest(xpath('/g:ROOT/g:Tienda/g:opc_activo/text()', col, array[array['g','http://localhost.coppel.com']])) as opc_acti,

		unnest(xpath('/g:ROOT/g:Tienda/g:idu_usuariocreacion/text()', col, array[array['g','http://localhost.coppel.com']])) as idu_usua

	FROM xmldata;

	IF EXISTS (SELECT tmp.tienda FROM tmp_tienda AS tmp)
	THEN
		INSERT INTO ctl_logtiendasconciliacion
		(num_tienda, fec_fechalog,opc_conciliado, opc_activo,fec_fechacreacion, idu_usuariocreacion)			
		SELECT 	tmp.tienda::INTEGER,tmp.fecha::DATE,tmp.opc_conci::BIT,tmp.opc_acti::bit,NOW()::DATE,tmp.idu_usua::INTEGER 
		FROM tmp_tienda AS tmp
		WHERE NOT EXISTS
		(SELECT num_tienda, fec_fechalog  
		FROM  ctl_logtiendasconciliacion 
		WHERE num_tienda = tmp.tienda::INTEGER AND fec_fechalog = tmp.fecha::DATE);
  		return TRUE;
	ELSE
		return FALSE;
	END IF;
END;
$_$;

ALTER FUNCTION public.fun_insertartiendaslog_xml(xml) OWNER TO postgres;