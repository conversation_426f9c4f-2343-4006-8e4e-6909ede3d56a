#
# Include this file in your .gitlab-ci.yml file to automate & integrate Checkmarx security scans.

variables:
    CX_FLOW_EXE_SAST: "java -jar /app/cx-flow.jar --spring.config.location=./application.yml"
    CHECKMARX_PROJECT_NAME: "$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"
    CHECKMARX_TEAM_ABS: "$CHECKMARX_TEAM_PATH$CHECKMARX_TEAM"


checkmarx-scan-mr-sast:
  stage: checkmarxSAST
  needs: []
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && 
            ($CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /\d{1,6}_\d{6}_(\w*Desarrollo\w*)/ || 
             $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /\d{1,6}_(\w*GamanSolutions)/ || 
             $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /(\w*develop\w*)/ ||
             $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /(\w*Desarrollo\w*)/ ||
             $CI_MERGE_REQUEST_TARGET_BRANCH_NAME =~ /(\w*master\w*)/) &&
             $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME !~ /(\w*master\w*)/'
  image:
    name: ${CHECKMARX_DOCKER_IMAGE}
    entrypoint: ['']
  variables:
    CHECKMARX_INCREMENTAL: "true"
    CHECKMARX_SETTINGS_OVERRIDE: "true"
    CHECKMARX_EXCLUDE_FILES: "scripts/*,README.md,Pack/*,Documentacion/*,Arquitectura/*,Codigo Servicio/wsConciliacion/WebContent/*,Codigo Servicio/wsConciliacion/build/*,Codigo Servicio/wsConciliacion/resources/*,Codigo Pagina/*"
#  cx_flow:
#    exclude_patterns:
#    - "scripts/*"
#    - "logs/*"
#    - "Documentacion/*"
#    - "Arquitectura/*"
#    - "Codigo Servicio/wsConciliacion/WebContent/*"
#    - "Codigo Servicio/wsConciliacion/build/*"
#    - "Codigo Servicio/wsConciliacion/resources/*"
#    - "Codigo Pagina/*"
  script:
    - cat ${CX_FLOW_CONFIG_SAST} > application.yml
    - ${CX_FLOW_EXE_SAST}
          --scan
          --app="${CI_PROJECT_NAME}"
          --namespace="${CI_PROJECT_NAMESPACE}"
          --repo-name="${CI_PROJECT_NAME}"
          --repo-url="${CI_REPOSITORY_URL}" 
          --cx-team="${CHECKMARX_TEAM_ABS}" 
          --cx-project="${CHECKMARX_PROJECT_NAME}" 
          --branch="${CI_MERGE_REQUEST_SOURCE_BRANCH_NAME}"
          --exclude="scripts/*,README.md,Pack/*,Documentacion/*,Arquitectura/*,Codigo Servicio/wsConciliacion/WebContent/*,Codigo Servicio/wsConciliacion/build/*,Codigo Servicio/wsConciliacion/resources/*,Codigo Pagina/*"
          --spring.profiles.active="${CX_FLOW_ENABLED_VULNERABILITY_SCANNERS}"
          --f=. 
          ${PARAMS}

  tags:
    - omnicanal


checkmarx-scan-master-sast:
  stage: checkmarxSAST
  needs: []
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && ($CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "main")'
  image:
    name: ${CHECKMARX_DOCKER_IMAGE}
    entrypoint: ['']
  variables:
    CHECKMARX_INCREMENTAL: "true"
    CHECKMARX_SETTINGS_OVERRIDE: "true"
    CHECKMARX_EXCLUDE_FILES: "scripts/*,README.md,Pack/*,Documentacion/*,Arquitectura/*,Codigo Servicio/wsConciliacion/WebContent/*,Codigo Servicio/wsConciliacion/build/*,Codigo Servicio/wsConciliacion/resources/*,Codigo Pagina/*"
#  cx_flow:
#    exclude_patterns:
#    - "scripts/*"
#    - "logs/*"
#    - "Documentacion/*"
#    - "Arquitectura/*"
#    - "Codigo Servicio/wsConciliacion/WebContent/*"
#    - "Codigo Servicio/wsConciliacion/build/*"
#    - "Codigo Servicio/wsConciliacion/resources/*"
#    - "Codigo Pagina/*"
  script:
    - cat ${CX_FLOW_CONFIG_SAST} > application.yml
    - ${CX_FLOW_EXE_SAST}
          --scan 
          --app="${CI_PROJECT_NAME}" 
          --namespace="${CI_PROJECT_NAMESPACE}" 
          --repo-name="${CI_PROJECT_NAME}" 
          --repo-url="${CI_REPOSITORY_URL}" 
          --cx-team="${CHECKMARX_TEAM_ABS}" 
          --cx-project="${CI_PROJECT_NAME}" 
          --branch="${CI_COMMIT_BRANCH}"
          --exclude="scripts/*,README.md,Pack/*,Documentacion/*,Arquitectura/*,Codigo Servicio/wsConciliacion/WebContent/*,Codigo Servicio/wsConciliacion/build/*,Codigo Servicio/wsConciliacion/resources/*,Codigo Pagina/*"
          --spring.profiles.active="${CX_FLOW_ENABLED_VULNERABILITY_SCANNERS}"
          --f=. 
          ${PARAMS}
  tags:
    - omnicanal


checkmarx-scan-commit-sast:
  stage: checkmarxSAST
  needs: []
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" && $CI_COMMIT_BRANCH != "master" && $CX_COMMIT_SCAN =~ /(?i)(\w*true\w*)/'
  image:   
    name: ${CHECKMARX_DOCKER_IMAGE}
    entrypoint: ['']
  variables:
    CHECKMARX_INCREMENTAL: "true"
    CHECKMARX_SETTINGS_OVERRIDE: "true"
    CHECKMARX_EXCLUDE_FILES: "scripts/*,README.md,Pack/*,Documentacion/*,Arquitectura/*,Codigo Servicio/wsConciliacion/WebContent/*,Codigo Servicio/wsConciliacion/build/*,Codigo Servicio/wsConciliacion/resources/*,Codigo Pagina/*"
#  cx_flow:
#    exclude_patterns:
#    - "scripts/*"
#    - "logs/*"
#    - "Documentacion/*"
#    - "Arquitectura/*"
#    - "Codigo Servicio/wsConciliacion/WebContent/*"
#    - "Codigo Servicio/wsConciliacion/build/*"
#    - "Codigo Servicio/wsConciliacion/resources/*"
#    - "Codigo Pagina/*"
    
  script:
    - cat ${CX_FLOW_CONFIG_SAST} > application.yml
    - ${CX_FLOW_EXE_SAST}
          --scan
          --app="${CI_PROJECT_NAME}" 
          --namespace="${CI_PROJECT_NAMESPACE}"
          --repo-name="${CI_PROJECT_NAME}"
          --repo-url="${CI_REPOSITORY_URL}" 
          --cx-team="${CHECKMARX_TEAM_ABS}" 
          --cx-project="${CHECKMARX_PROJECT_NAME}" 
          --branch="${CI_COMMIT_BRANCH}"
          --exclude="scripts/*,README.md,Pack/*,Documentacion/*,Arquitectura/*,Codigo Servicio/wsConciliacion/WebContent/*,Codigo Servicio/wsConciliacion/build/*,Codigo Servicio/wsConciliacion/resources/*,Codigo Pagina/*"
          --spring.profiles.active="${CX_FLOW_ENABLED_VULNERABILITY_SCANNERS}"
          --f=. 
          ${PARAMS}
  tags:
    - omnicanal
