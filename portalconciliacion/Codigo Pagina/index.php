 <?php
 /******************************************************************************************
 * Autor: <PERSON>
 * Fecha: 21/03/2014
 * Fecha Actualizacion: 10/11/2014
 * Lenguaje: PHP
 * Tipo: index principal para los Sistemas Coppel
 * Descripción: Archivo generado automaticamente con la herramienta Generar Estructura Basica
 *  			Generador de codigo desarrollado para optimizar tiempos en el desarrollo de
 *  			Sistemas en entorno web.
 * Nombre: index
 * Version: Beta
 ********************************************************************************************/
 session_name("Session_Portal_Conciliacion");
 session_start();
 header ('Content-type: text/html; charset=utf-8');
	
 //error_reporting(0);
	require_once ("../utilidadesweb/librerias/crypto/crypto.php");
	require_once '../utilidadesweb/librerias/data/odbcclient.php';
	require_once '../utilidadesweb/librerias/cnfg/conexiones.php';
	/*----------------------------------------------------------------------------------------
	 * El archivo debe recibir  un parámetro por POST que indique el índice del sistema
	 */
	  /*var_dump($_SERVER['SERVER_PORT']);
	  exit;*/
	 $json = '';
	 $iconoApp = '/plantilla/coppel/assets/img/icn_sc.ico';
	 $Session = "CruceL";//cambiar dependiendo del sistema
	 //$DirSis = 'sueldosespeciales';//cambiar por el nombre del directorio de trabajo del sistema origen
	 //$jsPrincipal = "frmCapturaDomicilio"; // Para que se ejecute al iniciar
	 $plantilla = "template";
	  $strValjson = "files/values/strval.json";
	 $strValue = "";
	/*----------------------------------------------------------------------------------------
	 * El archivo debe recibir  un parámetro por POST que indique el índice del sistema
	 */
	// require ('ajax/json/json_testdata.php');
	// $_POST['jsonInfo'] = getTestData();
			
		if(isset($_POST['jsonInfo']))
		{//no olvidar colocar la decodificacion
			$json = str_replace('\\/', '/',desencriptar($_POST['jsonInfo'])); // Se quitan las diagonales invertidas
			$json = json_decode($json, TRUE);
			
			if(!isset($json['USUARIO']['num_empleado'] ) ||
				!isset($json['USUARIO']['num_centro'] )){
				// Si no están declaradas las variables de sesión anteriores
				// Lanza un mensaje de error
				header ('Content-type: text/html; charset=utf-8');
				header('HTTP/1.0 404 Not Found');
				echo "<b><h1>Error 404 </h1><h2>Pagina No Encontrada</h2></b>La página a la cual quieres acceder no está disponible o no existe.";
				exit();
			}
			$_SESSION[$Session] = $json;
		}
		else {
			if(!isset($_SESSION[$Session]))//revizar mas delante
	 		{
	 			header ('Content-type: text/html; charset=utf-8');
				header('HTTP/1.0 404 Not Found');
				echo "<b><h1>Error 404 </h1><h2>Pagina No Encontrada</h2></b>La página a la cual quieres acceder no está disponible o no existe.";
				exit();
	 	 	}
			else {
				$plantilla = "error-404";
			}
		}
		
	/*----------------------------------------------------------------------------------------
	 * La carpeta utilidadesweb
	 * SistemasCoppel
	 */
	 $indexSistema = $_SESSION[$Session]['INDEX'];//"http://" . $_SERVER['SERVER_ADDR'] . rtrim(dirname($_SERVER['PHP_SELF']), '/\\') . '/';
	 $utilidadesweb = str_replace($_SESSION[$Session]['DIRSIS'], 'utilidadesweb', $indexSistema);//cambiar dependiedo del diorectori del sistema

	/*----------------------------------------------------------------------------------------
	 * Variables que definen la ubicación de la plantilla que se va a usar
	 * y 
	 * 
	 */
	 
		$url_template = '';
		$url_template = $utilidadesweb . 'plantilla/coppel/'.$plantilla.'.html';
		
		if($archivo_texto = fopen ("$url_template", "r"))
		{
			$contenido_archivo = '';
			while (!feof($archivo_texto)) {
				$contenido_archivo .= fgets($archivo_texto,4096);
			}
			fclose ($archivo_texto);
		}
		
		if($strValjson = fopen("$strValjson", "r"))
		{
			$strValue = '';
			while (!feof($strValjson)) {
				$strValue .= fgets($strValjson,4096);
			}
			fclose ($strValjson);
		}
		$contenido_archivo = str_replace('<!--url-->/', $utilidadesweb, $contenido_archivo); // Direccionando la ruta de las librerías

		/****************************************************************************************************/	
		//para agrgar las libs Extras que s enecesiten en tu sistema
		$LibsExtras = '';
		/*******************************************************************************************************/
		
	/* Modificar el título de la aplicación
	 */
	 
		$tituloSistema = ucwords(strtolower($_SESSION[$Session]['APLICACION'][0]['nom_nombresistema']));
		$contenido_archivo = str_replace('<!--%TituloSistema%-->', $tituloSistema, $contenido_archivo);
	
	
			$menu_sistema = array();
			$separador = '';
			$ind = 0;
			$indice = 0;
			$indexAnterior = '';
			$indiCierre = false;

			foreach ($_SESSION[$Session]['APLICACION'] as $opcion) {
					
				if(($opcion['indice'] == $opcion['nom_nombreaplicacion']) && $separador == '')
				{
					if($indiCierre == true){ $menu_sistema[$indice] .= ']}';$indice++;$indiCierre = false;}
					$menu_sistema[$indice] = '{"idu_sistema":"'.$opcion['idu_sistema'].'","indice":"'.$opcion['indice'].'","nom_nombresistema":"'.$opcion['nom_nombresistema'].'","nom_nombreaplicacion":"'.$opcion['nom_nombreaplicacion'].'","urlaplicacion":"'.$opcion['urlaplicacion'].'","ordenmenuaplicacion":"'.$opcion['ordenmenuaplicacion'].'","iconoaplicacion":"'.$opcion['iconoaplicacion'].'","desc_ayudaaplicacion":"'.$opcion['desc_ayudaaplicacion'].'","opciones":"0"}';
					$indice++;
				}
				else
					{
						
						if(($indexAnterior == $opcion['indice']) || ($indexAnterior == ''))
						{
							if($ind > 0)
							{
								$separador = ',';
							}
							else
							 {
							 	$menu_sistema[$indice] = '{"idu_sistema":"'.$opcion['idu_sistema'].'","indice":"'.$opcion['indice'].'","nom_nombresistema":"'.$opcion['nom_nombresistema'].'","nom_nombreaplicacion":"'.$opcion['nom_nombreaplicacion'].'","urlaplicacion":"'.$opcion['urlaplicacion'].'","ordenmenuaplicacion":"'.$opcion['ordenmenuaplicacion'].'","iconoaplicacion":"'.$opcion['iconoaplicacion'].'","desc_ayudaaplicacion":"'.$opcion['desc_ayudaaplicacion'].'","opciones":"1","submenu":[';
							 }
							
							$menu_sistema[$indice] .= $separador . json_encode($opcion);
							$ind++;
							$indexAnterior = $opcion['indice'];
							$indiCierre = true;
						}
						else {
							
							$separador = '';
							$ind = 0;
							//if($menu_sistema[$indice] != '')
							$menu_sistema[$indice] .= ']}';
							$indice++;
							if($opcion['indice'] != $opcion['nom_nombreaplicacion'])
							{
								$menu_sistema[$indice] = '{"idu_sistema":"'.$opcion['idu_sistema'].'","indice":"'.$opcion['indice'].'","nom_nombresistema":"'.$opcion['nom_nombresistema'].'","nom_nombreaplicacion":"'.$opcion['nom_nombreaplicacion'].'","urlaplicacion":"'.$opcion['urlaplicacion'].'","ordenmenuaplicacion":"'.$opcion['ordenmenuaplicacion'].'","iconoaplicacion":"'.$opcion['iconoaplicacion'].'","desc_ayudaaplicacion":"'.$opcion['desc_ayudaaplicacion'].'","opciones":"1","submenu":[';
								$menu_sistema[$indice] .= $separador . json_encode($opcion);
								$ind++;
								$indexAnterior = $opcion['indice'];
								$indiCierre = true;
							}
							else
							{
								$menu_sistema[$indice] = '{"idu_sistema":"'.$opcion['idu_sistema'].'","indice":"'.$opcion['indice'].'","nom_nombresistema":"'.$opcion['nom_nombresistema'].'","nom_nombreaplicacion":"'.$opcion['nom_nombreaplicacion'].'","urlaplicacion":"'.$opcion['urlaplicacion'].'","ordenmenuaplicacion":"'.$opcion['ordenmenuaplicacion'].'","iconoaplicacion":"'.$opcion['iconoaplicacion'].'","desc_ayudaaplicacion":"'.$opcion['desc_ayudaaplicacion'].'","opciones":"0"}';
								$indice++;
								$indexAnterior = '';
								$indiCierre = false;
							}
						}
					}
			}
			if($indiCierre == true) $menu_sistema[$indice--] .= ']}';
			
			$Menu = '{"menu":[';
			$separador = '';
			$ind = 0;
			foreach($menu_sistema as $datosOpc)
			{
				if($ind > 0)
				{
					$separador = ',';
				}
				$Menu .= $separador . $datosOpc;
				$ind++;
			}
			$Menu .= ']}';

	// Establecer campos hidden que pueden ser útiles en la aplicación
		$contenido_archivo = str_replace('<!--%hid_index%-->',$indexSistema, $contenido_archivo);
		//$contenido_archivo = str_replace('<!--%hid_iduempleado%-->', /*$aUsuario['numeroempleado']*/'null', $contenido_archivo);

		$dia = array("Domingo","Lunes","Martes","Miercoles","Jueves","Viernes","Sábado");
		$mes = array("Meses","Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre");

		$contenido_archivo = str_replace('<!--%Fecha%-->',$dia[@date('w',time())] . @date(' d \d\e ',time()). $mes[@date('m',time()) * 1] . @date(' \d\e Y',time()), $contenido_archivo);
				
		$contenido_archivo = str_replace('<!--%Nom_Usuraio%-->', $_SESSION[$Session]['USUARIO']['nom_empleado'] . " " . $_SESSION[$Session]['USUARIO']['nom_apellidopaterno'] . " " . $_SESSION[$Session]['USUARIO']['nom_apellidomaterno'], $contenido_archivo);
		
		$contenido_archivo = str_replace('<!--Menu_Sistema-->', $Menu, $contenido_archivo);

		//$contenido_archivo = str_replace('<!--%UrlImagenMaketa%-->', 'files/img/icns_maketa.png', $contenido_archivo);

		//$contenido_archivo = str_replace('<!--%IP_Cliente%-->', $_SESSION[$Session]->clientIP, $contenido_archivo);
	
		//colocar js con las funciones a cargar al inicviar el sistema
		//$contenido_archivo = str_replace('<!--%dirjs%-->', $LibsExtras.'<script src="files/js/'.$jsPrincipal.'.js"></script>', $contenido_archivo);
		
		$avatar = "user" . $_SESSION[$Session]['USUARIO']['sexo'] .".jpg";
		$saludo = "Bienvenid";
		$saludo .= ($_SESSION[$Session]['USUARIO']['sexo'] == '1')? 'a,':'o,'; 
		
		$contenido_archivo = str_replace('<!--%avatar%-->', $avatar, $contenido_archivo);
		$contenido_archivo = str_replace('<!--%Saludo%-->', $saludo, $contenido_archivo);
		
		// default
		$iconoApp = $utilidadesweb . $iconoApp;//icono default
		//$iconoApp = 'files/img/appicn.ico';//icono propio
		$contenido_archivo = str_replace('<!--icono-->', $iconoApp, $contenido_archivo);
		
		$contenido_archivo = str_replace('<!--unirTitulo-->', 'false', $contenido_archivo);//union del Indice y la Aplicacion desactivada
		$contenido_archivo = str_replace('<!--session_name-->',$Session , $contenido_archivo);
		$contenido_archivo = str_replace('<!--session_Org-->',$_SESSION[$Session]['SESSION_ORIGEN'] , $contenido_archivo);
		//$contenido_archivo = str_replace('<!--var_Global-->', '/*Aqui van variables Globales para cada Sisetma*/', $contenido_archivo);
		$contenido_archivo = str_replace('<!--var_Global-->', 'var usuario = '. $_SESSION[$Session]['USUARIO']['num_empleado'].";", $contenido_archivo);
		$OctBtn = (strpos( $_SESSION[$Session]['INDEX'], "http://".$_SERVER['SERVER_NAME']) === FALSE)? "":"ocultabtn('close','block');";
		$contenido_archivo = str_replace('<!--fCerrarSession-->',$OctBtn, $contenido_archivo);
		if($strValue != "")
		{
			 $contenido_archivo = str_replace('"<!--str_values-->"', $strValue,$contenido_archivo);
			
	  		 $strValue = str_replace(chr (13), '',$strValue);
			 $strValue = str_replace(chr (10), '',$strValue);
			 $strValue = str_replace(chr (9), '',$strValue);
			 
			 $strValue = str_replace('{', '{"',$strValue);
			 
			 $strValue = str_replace(':"', '":"',$strValue);
			 $strValue = str_replace(': "', '":"',$strValue);
			 
			 $strValue = str_replace('",', '","',$strValue);
			
			 $_SESSION[$Session]['LengStr'] = new stdClass();
			 $_SESSION[$Session]['LengStr'] = json_decode($strValue);
		}
		$contenido_archivo = str_replace('<!--Emp_Fto-->', $_SESSION[$Session]['USUARIO']['num_empleado'], $contenido_archivo);
	// Imprimir el contenido del archivo
		echo $contenido_archivo;
		
		//$_SESSION[$Session]['utilidadesweb'] = $utilidadesweb;
?>