debugger;
	var correcto = 0;
	var alerta = 0;
	var incorrecto = 0;
	var incorrectoMensaje = 0;

	var ruta = "files/data/wsconciliacion.php";
	
	function llamadaAjax(funcion)
	{
			var parametros =
			{
                "funcion" : funcion
	        };
			$.ajax({
				url: ruta,
		        type: "post",
				data: parametros,
				async: true,
		        success: function (response)
		        {
		           switch(funcion)
		           {
		           	case "obtenerFecha":
		           		escribirFecha(response);
		           	break;
					case "consultarEstadoConciliacion":
						escribirEstadoConciliacion(response);
					break;
		           }
		        },
		        error: function(jqXHR, textStatus, errorThrown)
		        {
		           mostrarModal(incorrecto, "Error");
		        }
	    	});
	}
	function escribirEstadoConciliacion(response){
		var mensaje = response.split("|");
		if(mensaje[0] == '5'){
			$(".estado").text("Estado: " + mensaje[1]);
		}
		else{
			escribirMensajeConciliacion(response);
		}
	}
	function llamadaAjaxGenerarConciliacion(funcion)
	{
			var parametros =
			{
                "funcion" : funcion
	        };
			$.ajax({
				url: ruta,
		        type: "post",
				data: parametros,
				async: true
	    	});
	}

	function reenvioDeCorreo(funcion, confirmacion)
	{
		var numeroMensaje = '',
			descripcionMensaje = '';

		$(".loader").css("display","block");
		$(".estado").text("Estado: Reenviando correo electronico.");
		$("#contenedorModalErrorMensaje").css("display","none");
		$("#fade").css("display","none");
		var parametros =
			{
                "funcion" : funcion,
                "confirmacion" : confirmacion
	        };
			$.ajax({
				url: ruta,
		        type: "post",
				data: parametros,
				async: true,
		        success: function (respuesta)
		        {
					var mensaje = respuesta.split("|");
					$(".loader").css("display","none");
					numeroMensaje = mensaje[0];
					descripcionMensaje = mensaje[1];

					switch (numeroMensaje) {
						case "0":
							mostrarModal(0, descripcionMensaje);
							break;
						case "1":
							mostrarModal(1, descripcionMensaje);
							break;
						case "2":
							mostrarModal(2, descripcionMensaje);
							break;
						case "3":
							mostrarModal(3, descripcionMensaje);
							break;
						case "4":
							mostrarModal(4, descripcionMensaje);
							break;
						default:
							alert("error al hacer la llamada al servicio web");
					}
					$("#btnConciliar")
							.removeClass("btn-default")
							.addClass("btn-primary")
							.removeAttr("disabled","true");
					$(".estado").text("Estado: En espera");
		        },
		        error: function(jqXHR, textStatus, errorThrown)
		        {
							$("#btnConciliar")
									.removeClass("btn-default")
									.addClass("btn-primary")
									.attr("disabled","false");
		           console.log(textStatus, errorThrown);
		        }
	    	});
	}

	function mostrarModal(tipoModal, mensaje)
	{
		$("#fade").css("display","block");
		$(".mensaje-escrito").text(mensaje);
	    switch(tipoModal){
			case correcto:
			$("#contenedorModalCorrecto").css("display","block");
			break;
			case alerta:
			$("#contenedorModalAlerta").css("display","block");
			break;
			case incorrecto:
			$("#contenedorModalError").css("display","block");
			break;
			case incorrectoMensaje:
			$("#contenedorModalErrorMensaje").css("display","block");
			break;
			default:
			break;
		}
	}

	function ocultarModal()
	{
		$("#contenedorModalCorrecto").css("display","none");
		$("#contenedorModalAlerta").css("display","none");
		$("#contenedorModalError").css("display","none");
		$("#fade").css("display","none");
	}

	function generarConciliacion(elem)
	{
		elem
			.attr("disabled","true")
			.removeClass("btn-primary")
			.addClass("btn-default");
		$(".loader").css("display","block");
		$(".estado").text("Estado: Conciliando");
		intervaloEstado();
		llamadaAjaxGenerarConciliacion("generarConciliacion");
	}

	function escribirMensajeConciliacion(datos)
	{
		clearInterval(intervalo);
		var numeroMensaje = ''
				,descripcionMensaje = '';
		var mensaje = datos.split("|");
		$(".loader").css("display","none");

		numeroMensaje = mensaje[0];
		descripcionMensaje = mensaje[1];

		switch (numeroMensaje) {
			case "0":
				mostrarModal(0, descripcionMensaje);
				break;
			case "1":
				mostrarModal(1, descripcionMensaje);
				break;
			case "2":
				mostrarModal(2, descripcionMensaje);
				break;
			case "3":
				mostrarModal(3, descripcionMensaje);
				break;
			default:
				alert("error al hacer la llamada al servicio web");
				break;
		}
		$("#btnConciliar")
				.removeClass("btn-default")
				.addClass("btn-primary")
				.removeAttr("disabled","true");
		$(".estado").text("Estado: En espera");
		obtenerFecha();
	}

	function obtenerFecha()
	{
		llamadaAjax("obtenerFecha");
	}
	
	function escribirFecha(datos){
		var mensaje = datos.split("|");
		if(mensaje[0] == 0)
		{
			$(".fecha-conciliacion").text("Fecha a conciliar: " + mensaje[1]);
		}
		if(mensaje[0] == 2)
		{
			mostrarModal(2, mensaje[1]);
		}
	}
	var intervalo;
	function intervaloEstado()
	{
		intervalo = setInterval(
			function()
			{
				llamadaAjax("consultarEstadoConciliacion");
			}
		,7000);
	}
	$( document ).ready(function()
	{
		correcto = 0;
		alerta = 1;
		incorrecto = 2;
		incorrectoMensaje = 3;
    	obtenerFecha();
	});