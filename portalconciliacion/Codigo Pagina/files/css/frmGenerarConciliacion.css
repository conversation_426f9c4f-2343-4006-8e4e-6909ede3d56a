*{
	margin:0;
	padding: 0px;
	font-family: sans-serif;
 }
 body{
 	background: rgb(235,235,235);
 }
 .formulario-peticion{
 	background: white;
 	margin:200px auto;
 	text-align: center;
 	height: 300px;
 	width: 500;
 	box-shadow: 0px 0px 20px 3px #888888;
 	border-radius: 10px;
 }
 .titulo{
 	float: left;
 	margin:20px;
	cursor:default !important;
 }
.barra-estado{
	float:left;
	border-bottom:1px solid rgb(150,150,150);
	width: 100%;
	height: 50px;
}
.fecha-conciliacion{
	line-height: 30px;
	float: right;
	padding-right: 20px;
	cursor:default !important;
}
.button:hover{
	background: rgba(255,255,0,.3);
}
.cargando{
	width: 50px;
	height: 50px;
	border-radius: 100%;
	background: red;
	margin:0 auto;
	margin-top: 50px;
}
.cargando-2{
	position: relative;
	width: 30px;
	height: 30px;
	border-radius: 100%;
	background: yellow;
	margin:0 auto;
	margin-top: 50px;
}
.estado{
	line-height: 40px;
	cursor:default !important;
}
.footer{
	margin:0 auto !important;
	text-align: center;
}
.separador{
	width: 100%;
	height: 70px;
	background: white;
	margin-top: 50px;
}
.contenedor{
	position: fixed;
	width: 150px;
	height: 150px;
}
.disabled{
	cursor: default;
}
.loader {
  display: none;
  font-size: 10px;
  margin: 0 auto;
  text-indent: -9999em;
  width: 5em;
  height: 5em;
  border-radius: 50%;
  background: rgb(100,100,100);
  background: -moz-linear-gradient(left, rgb(100,100,100) 10%, rgba(255, 255, 255, 0) 42%);
  background: -webkit-linear-gradient(left, rgb(100,100,100) 10%, rgba(255, 255, 255, 0) 42%);
  background: -o-linear-gradient(left, rgb(100,100,100) 10%, rgba(255, 255, 255, 0) 42%);
  background: -ms-linear-gradient(left, rgb(100,100,100) 10%, rgba(255, 255, 255, 0) 42%);
  background: linear-gradient(to right, rgb(100,100,100) 10%, rgba(255, 255, 255, 0) 42%);
  position: relative;
  -webkit-animation: load3 1.4s infinite linear;
  animation: load3 1.4s infinite linear;
}
.loader:before {
  width: 50%;
  height: 50%;
  background: rgb(100,100,100);
  border-radius: 100% 0 0 0;
  position: absolute;
  top: 0;
  left: 0;
  content: '';
}
.loader:after {
  background: white;
  width: 75%;
  height: 75%;
  border-radius: 50%;
  content: '';
  margin: auto;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
@-webkit-keyframes load3 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes load3 {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.overlay{
     display: none;
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background: #000;
     z-index:1001;
     opacity:.75;
     -moz-opacity: 0.75;
     filter: alpha(opacity=75);
}
.modal {
     display: none;
     position: absolute;
	 margin-left: 0px;
     top: 35%;
     left: 35%;
     width: 400px;
     height: 150px;
     background: #fff;
     color: #333;
     z-index:1002;
     overflow: auto;
     text-align: center;
}
.contenedor-boton{
	bottom: 20px;
	position: absolute;
	margin: 0 auto;
	width: 100%;
	text-align: center;
}
.boton-modal{
	margin: 0;
	bottom: 10px;
	left:145px;
    position: absolute;
}
.boton-modal-aceptar{
	left:72;
}
.boton-modal-cancelar{
	left:211;
}
