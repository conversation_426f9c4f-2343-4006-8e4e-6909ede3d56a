<?php
include '../../ajax/json/config.php';
  

  ini_set("soap.wsdl_cache_enabled", "0");
  ini_set('max_execution_time', 0);
ini_set('memory_limit', '512M');

if(isset($_POST["funcion"])){
  switch ($_POST["funcion"]) {
    case 'obtenerFecha':
        obtenerFecha();
      break;
    case 'generarConciliacion':
        generarConciliacion();
      break;
    case 'reenvioDeCorreo':
        reenvioDeCorreo($_POST["confirmacion"]);
      break;
	case 'consultarEstadoConciliacion':
		consultarEstadoConciliacion();
	  break;
    default:
        print_r("La funcion no existe.");
      break;
  }
}
else{
  print_r("No existe funcion");
}
	function consultarEstadoConciliacion(){
      global $ipWsconciliacion;
      $client = new SoapClient($ipWsconciliacion);
      $result = $client->consultarEstadoConciliacion();
      $xml = $result->consultarEstadoConciliacionReturn;
      print_r($xml);
	}
	
  function obtenerFecha(){

      global $ipWsconciliacion;
      $client = new SoapClient($ipWsconciliacion);
      $result = $client->obtenerUltimaFecha();
      $xml = $result->obtenerUltimaFechaReturn;
      print_r($xml);
  }

  function generarConciliacion(){
      global $ipWsconciliacion;
      $client = new SoapClient($ipWsconciliacion);
      $result = $client->generarConciliacion();
      //$xml = $result->generarConciliacionReturn;
      //print_r($xml);
  }

  function reenvioDeCorreo($Confirmacion){
     global $ipWsconciliacion;
      $client = new SoapClient($ipWsconciliacion);
      $params = array(
          'peticion' => $Confirmacion
      );
      $response = $client->__soapCall("reenviarCorreo", array($params));
      $xml = $response->reenviarCorreoReturn;
      print_r($xml);
  }
  // $version = apache_get_version();
  // echo "ESTA ES LA VERSION"."$version\n";
?>